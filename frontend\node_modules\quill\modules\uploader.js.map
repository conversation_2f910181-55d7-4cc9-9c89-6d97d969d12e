{"version": 3, "file": "uploader.js", "names": ["Delta", "Emitter", "<PERSON><PERSON><PERSON>", "Uploader", "constructor", "quill", "options", "root", "addEventListener", "e", "preventDefault", "native", "document", "caretRangeFromPoint", "clientX", "clientY", "caretPositionFromPoint", "position", "createRange", "setStart", "offsetNode", "offset", "setEnd", "normalized", "selection", "normalizeNative", "range", "normalizedToRange", "dataTransfer", "files", "upload", "uploads", "Array", "from", "for<PERSON>ach", "file", "mimetypes", "includes", "type", "push", "length", "handler", "call", "DEFAULTS", "scroll", "query", "promises", "map", "Promise", "resolve", "reader", "FileReader", "onload", "result", "readAsDataURL", "all", "then", "images", "update", "reduce", "delta", "image", "insert", "retain", "index", "delete", "updateContents", "sources", "USER", "setSelection", "SILENT"], "sources": ["../../src/modules/uploader.ts"], "sourcesContent": ["import Delta from 'quill-delta';\nimport type Quill from '../core/quill.js';\nimport Emitter from '../core/emitter.js';\nimport Module from '../core/module.js';\nimport type { Range } from '../core/selection.js';\n\ninterface UploaderOptions {\n  mimetypes: string[];\n  handler: (this: { quill: Quill }, range: Range, files: File[]) => void;\n}\n\nclass Uploader extends Module<UploaderOptions> {\n  static DEFAULTS: UploaderOptions;\n\n  constructor(quill: Quill, options: Partial<UploaderOptions>) {\n    super(quill, options);\n    quill.root.addEventListener('drop', (e) => {\n      e.preventDefault();\n      let native: ReturnType<typeof document.createRange> | null = null;\n      if (document.caretRangeFromPoint) {\n        native = document.caretRangeFromPoint(e.clientX, e.clientY);\n        // @ts-expect-error\n      } else if (document.caretPositionFromPoint) {\n        // @ts-expect-error\n        const position = document.caretPositionFromPoint(e.clientX, e.clientY);\n        native = document.createRange();\n        native.setStart(position.offsetNode, position.offset);\n        native.setEnd(position.offsetNode, position.offset);\n      }\n\n      const normalized = native && quill.selection.normalizeNative(native);\n      if (normalized) {\n        const range = quill.selection.normalizedToRange(normalized);\n        if (e.dataTransfer?.files) {\n          this.upload(range, e.dataTransfer.files);\n        }\n      }\n    });\n  }\n\n  upload(range: Range, files: FileList | File[]) {\n    const uploads: File[] = [];\n    Array.from(files).forEach((file) => {\n      if (file && this.options.mimetypes?.includes(file.type)) {\n        uploads.push(file);\n      }\n    });\n    if (uploads.length > 0) {\n      // @ts-expect-error Fix me later\n      this.options.handler.call(this, range, uploads);\n    }\n  }\n}\n\nUploader.DEFAULTS = {\n  mimetypes: ['image/png', 'image/jpeg'],\n  handler(range: Range, files: File[]) {\n    if (!this.quill.scroll.query('image')) {\n      return;\n    }\n    const promises = files.map<Promise<string>>((file) => {\n      return new Promise((resolve) => {\n        const reader = new FileReader();\n        reader.onload = () => {\n          resolve(reader.result as string);\n        };\n        reader.readAsDataURL(file);\n      });\n    });\n    Promise.all(promises).then((images) => {\n      const update = images.reduce((delta: Delta, image) => {\n        return delta.insert({ image });\n      }, new Delta().retain(range.index).delete(range.length)) as Delta;\n      this.quill.updateContents(update, Emitter.sources.USER);\n      this.quill.setSelection(\n        range.index + images.length,\n        Emitter.sources.SILENT,\n      );\n    });\n  },\n};\n\nexport default Uploader;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAE/B,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,MAAM,MAAM,mBAAmB;AAQtC,MAAMC,QAAQ,SAASD,MAAM,CAAkB;EAG7CE,WAAWA,CAACC,KAAY,EAAEC,OAAiC,EAAE;IAC3D,KAAK,CAACD,KAAK,EAAEC,OAAO,CAAC;IACrBD,KAAK,CAACE,IAAI,CAACC,gBAAgB,CAAC,MAAM,EAAGC,CAAC,IAAK;MACzCA,CAAC,CAACC,cAAc,CAAC,CAAC;MAClB,IAAIC,MAAsD,GAAG,IAAI;MACjE,IAAIC,QAAQ,CAACC,mBAAmB,EAAE;QAChCF,MAAM,GAAGC,QAAQ,CAACC,mBAAmB,CAACJ,CAAC,CAACK,OAAO,EAAEL,CAAC,CAACM,OAAO,CAAC;QAC3D;MACF,CAAC,MAAM,IAAIH,QAAQ,CAACI,sBAAsB,EAAE;QAC1C;QACA,MAAMC,QAAQ,GAAGL,QAAQ,CAACI,sBAAsB,CAACP,CAAC,CAACK,OAAO,EAAEL,CAAC,CAACM,OAAO,CAAC;QACtEJ,MAAM,GAAGC,QAAQ,CAACM,WAAW,CAAC,CAAC;QAC/BP,MAAM,CAACQ,QAAQ,CAACF,QAAQ,CAACG,UAAU,EAAEH,QAAQ,CAACI,MAAM,CAAC;QACrDV,MAAM,CAACW,MAAM,CAACL,QAAQ,CAACG,UAAU,EAAEH,QAAQ,CAACI,MAAM,CAAC;MACrD;MAEA,MAAME,UAAU,GAAGZ,MAAM,IAAIN,KAAK,CAACmB,SAAS,CAACC,eAAe,CAACd,MAAM,CAAC;MACpE,IAAIY,UAAU,EAAE;QACd,MAAMG,KAAK,GAAGrB,KAAK,CAACmB,SAAS,CAACG,iBAAiB,CAACJ,UAAU,CAAC;QAC3D,IAAId,CAAC,CAACmB,YAAY,EAAEC,KAAK,EAAE;UACzB,IAAI,CAACC,MAAM,CAACJ,KAAK,EAAEjB,CAAC,CAACmB,YAAY,CAACC,KAAK,CAAC;QAC1C;MACF;IACF,CAAC,CAAC;EACJ;EAEAC,MAAMA,CAACJ,KAAY,EAAEG,KAAwB,EAAE;IAC7C,MAAME,OAAe,GAAG,EAAE;IAC1BC,KAAK,CAACC,IAAI,CAACJ,KAAK,CAAC,CAACK,OAAO,CAAEC,IAAI,IAAK;MAClC,IAAIA,IAAI,IAAI,IAAI,CAAC7B,OAAO,CAAC8B,SAAS,EAAEC,QAAQ,CAACF,IAAI,CAACG,IAAI,CAAC,EAAE;QACvDP,OAAO,CAACQ,IAAI,CAACJ,IAAI,CAAC;MACpB;IACF,CAAC,CAAC;IACF,IAAIJ,OAAO,CAACS,MAAM,GAAG,CAAC,EAAE;MACtB;MACA,IAAI,CAAClC,OAAO,CAACmC,OAAO,CAACC,IAAI,CAAC,IAAI,EAAEhB,KAAK,EAAEK,OAAO,CAAC;IACjD;EACF;AACF;AAEA5B,QAAQ,CAACwC,QAAQ,GAAG;EAClBP,SAAS,EAAE,CAAC,WAAW,EAAE,YAAY,CAAC;EACtCK,OAAOA,CAACf,KAAY,EAAEG,KAAa,EAAE;IACnC,IAAI,CAAC,IAAI,CAACxB,KAAK,CAACuC,MAAM,CAACC,KAAK,CAAC,OAAO,CAAC,EAAE;MACrC;IACF;IACA,MAAMC,QAAQ,GAAGjB,KAAK,CAACkB,GAAG,CAAmBZ,IAAI,IAAK;MACpD,OAAO,IAAIa,OAAO,CAAEC,OAAO,IAAK;QAC9B,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,MAAM,GAAG,MAAM;UACpBH,OAAO,CAACC,MAAM,CAACG,MAAgB,CAAC;QAClC,CAAC;QACDH,MAAM,CAACI,aAAa,CAACnB,IAAI,CAAC;MAC5B,CAAC,CAAC;IACJ,CAAC,CAAC;IACFa,OAAO,CAACO,GAAG,CAACT,QAAQ,CAAC,CAACU,IAAI,CAAEC,MAAM,IAAK;MACrC,MAAMC,MAAM,GAAGD,MAAM,CAACE,MAAM,CAAC,CAACC,KAAY,EAAEC,KAAK,KAAK;QACpD,OAAOD,KAAK,CAACE,MAAM,CAAC;UAAED;QAAM,CAAC,CAAC;MAChC,CAAC,EAAE,IAAI7D,KAAK,CAAC,CAAC,CAAC+D,MAAM,CAACrC,KAAK,CAACsC,KAAK,CAAC,CAACC,MAAM,CAACvC,KAAK,CAACc,MAAM,CAAC,CAAU;MACjE,IAAI,CAACnC,KAAK,CAAC6D,cAAc,CAACR,MAAM,EAAEzD,OAAO,CAACkE,OAAO,CAACC,IAAI,CAAC;MACvD,IAAI,CAAC/D,KAAK,CAACgE,YAAY,CACrB3C,KAAK,CAACsC,KAAK,GAAGP,MAAM,CAACjB,MAAM,EAC3BvC,OAAO,CAACkE,OAAO,CAACG,MAClB,CAAC;IACH,CAAC,CAAC;EACJ;AACF,CAAC;AAED,eAAenE,QAAQ", "ignoreList": []}