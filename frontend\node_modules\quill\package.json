{"name": "quill", "version": "2.0.3", "description": "Your powerful, rich text editor", "author": "<PERSON> <<EMAIL>>", "homepage": "https://quilljs.com", "main": "quill.js", "type": "module", "dependencies": {"eventemitter3": "^5.0.1", "lodash-es": "^4.17.21", "parchment": "^3.0.0", "quill-delta": "^5.1.0"}, "devDependencies": {"@babel/cli": "^7.23.9", "@babel/core": "^7.24.0", "@babel/preset-env": "^7.24.0", "@babel/preset-typescript": "^7.23.3", "@playwright/test": "1.44.1", "@types/highlight.js": "^9.12.4", "@types/lodash-es": "^4.17.12", "@types/node": "^20.10.0", "@types/webpack": "^5.28.5", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitest/browser": "^1.1.3", "babel-loader": "^9.1.3", "babel-plugin-transform-define": "^2.1.4", "css-loader": "^6.10.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-resolver-webpack": "^0.13.8", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-require-extensions": "^0.1.3", "glob": "10.4.2", "highlight.js": "^9.18.1", "html-loader": "^4.2.0", "html-webpack-plugin": "^5.5.3", "jsdom": "^22.1.0", "mini-css-extract-plugin": "^2.7.6", "prettier": "^3.0.3", "source-map-loader": "^5.0.0", "style-loader": "^3.3.3", "stylus": "^0.62.0", "stylus-loader": "^7.1.3", "svgo": "^3.2.0", "terser-webpack-plugin": "^5.3.9", "transpile-webpack-plugin": "^1.1.3", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "typescript": "^5.4.2", "vitest": "^1.1.3", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "webpack-merge": "^5.10.0"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/slab/quill.git", "directory": "packages/quill"}, "bugs": {"url": "https://github.com/slab/quill/issues"}, "prettier": {"singleQuote": true}, "browserslist": ["defaults"], "scripts": {"build": "./scripts/build production", "lint": "run-s lint:*", "lint:eslint": "eslint .", "lint:tsc": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "start": "[[ -z \"$npm_package_config_ports_webpack\" ]] && webpack-dev-server || webpack-dev-server --port $npm_package_config_ports_webpack", "test": "run-s test:*", "test:unit": "vitest --config test/unit/vitest.config.ts", "test:fuzz": "vitest --config test/fuzz/vitest.config.ts", "test:e2e": "playwright test"}, "keywords": ["quill", "editor", "rich text", "wysiwyg", "operational transformation", "ot", "framework"], "engines": {"npm": ">=8.2.3"}, "engineStrict": true}