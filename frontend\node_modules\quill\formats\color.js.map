{"version": 3, "file": "color.js", "names": ["ClassAttributor", "<PERSON><PERSON>", "StyleAttributor", "ColorAttributor", "value", "domNode", "startsWith", "replace", "hex", "split", "map", "component", "parseInt", "toString", "slice", "join", "ColorClass", "scope", "INLINE", "ColorStyle"], "sources": ["../../src/formats/color.ts"], "sourcesContent": ["import { ClassAttributor, Scope, StyleAttributor } from 'parchment';\n\nclass ColorAttributor extends StyleAttributor {\n  value(domNode: HTMLElement) {\n    let value = super.value(domNode) as string;\n    if (!value.startsWith('rgb(')) return value;\n    value = value.replace(/^[^\\d]+/, '').replace(/[^\\d]+$/, '');\n    const hex = value\n      .split(',')\n      .map((component) => `00${parseInt(component, 10).toString(16)}`.slice(-2))\n      .join('');\n    return `#${hex}`;\n  }\n}\n\nconst ColorClass = new ClassAttributor('color', 'ql-color', {\n  scope: Scope.INLINE,\n});\nconst ColorStyle = new ColorAttributor('color', 'color', {\n  scope: Scope.INLINE,\n});\n\nexport { ColorAttributor, ColorClass, ColorStyle };\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,KAAK,EAAEC,eAAe,QAAQ,WAAW;AAEnE,MAAMC,eAAe,SAASD,eAAe,CAAC;EAC5CE,KAAKA,CAACC,OAAoB,EAAE;IAC1B,IAAID,KAAK,GAAG,KAAK,CAACA,KAAK,CAACC,OAAO,CAAW;IAC1C,IAAI,CAACD,KAAK,CAACE,UAAU,CAAC,MAAM,CAAC,EAAE,OAAOF,KAAK;IAC3CA,KAAK,GAAGA,KAAK,CAACG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;IAC3D,MAAMC,GAAG,GAAGJ,KAAK,CACdK,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAAEC,SAAS,IAAM,KAAIC,QAAQ,CAACD,SAAS,EAAE,EAAE,CAAC,CAACE,QAAQ,CAAC,EAAE,CAAE,EAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CACzEC,IAAI,CAAC,EAAE,CAAC;IACX,OAAQ,IAAGP,GAAI,EAAC;EAClB;AACF;AAEA,MAAMQ,UAAU,GAAG,IAAIhB,eAAe,CAAC,OAAO,EAAE,UAAU,EAAE;EAC1DiB,KAAK,EAAEhB,KAAK,CAACiB;AACf,CAAC,CAAC;AACF,MAAMC,UAAU,GAAG,IAAIhB,eAAe,CAAC,OAAO,EAAE,OAAO,EAAE;EACvDc,KAAK,EAAEhB,KAAK,CAACiB;AACf,CAAC,CAAC;AAEF,SAASf,eAAe,EAAEa,UAAU,EAAEG,UAAU", "ignoreList": []}