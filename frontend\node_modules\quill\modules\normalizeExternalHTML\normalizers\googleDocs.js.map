{"version": 3, "file": "googleDocs.js", "names": ["normalWeightRegexp", "blockTagNames", "isBlockElement", "element", "includes", "tagName", "normalizeEmptyLines", "doc", "Array", "from", "querySelectorAll", "filter", "br", "previousElementSibling", "nextElement<PERSON><PERSON>ling", "for<PERSON>ach", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "normalizeFontWeight", "node", "getAttribute", "match", "fragment", "createDocumentFragment", "append", "childNodes", "<PERSON><PERSON><PERSON><PERSON>", "normalize", "querySelector"], "sources": ["../../../../src/modules/normalizeExternalHTML/normalizers/googleDocs.ts"], "sourcesContent": ["const normalWeightRegexp = /font-weight:\\s*normal/;\nconst blockTagNames = ['P', 'OL', 'UL'];\n\nconst isBlockElement = (element: Element | null) => {\n  return element && blockTagNames.includes(element.tagName);\n};\n\nconst normalizeEmptyLines = (doc: Document) => {\n  Array.from(doc.querySelectorAll('br'))\n    .filter(\n      (br) =>\n        isBlockElement(br.previousElementSibling) &&\n        isBlockElement(br.nextElementSibling),\n    )\n    .forEach((br) => {\n      br.parentNode?.removeChild(br);\n    });\n};\n\nconst normalizeFontWeight = (doc: Document) => {\n  Array.from(doc.querySelectorAll('b[style*=\"font-weight\"]'))\n    .filter((node) => node.getAttribute('style')?.match(normalWeightRegexp))\n    .forEach((node) => {\n      const fragment = doc.createDocumentFragment();\n      fragment.append(...node.childNodes);\n      node.parentNode?.replaceChild(fragment, node);\n    });\n};\n\nexport default function normalize(doc: Document) {\n  if (doc.querySelector('[id^=\"docs-internal-guid-\"]')) {\n    normalizeFontWeight(doc);\n    normalizeEmptyLines(doc);\n  }\n}\n"], "mappings": "AAAA,MAAMA,kBAAkB,GAAG,uBAAuB;AAClD,MAAMC,aAAa,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;AAEvC,MAAMC,cAAc,GAAIC,OAAuB,IAAK;EAClD,OAAOA,OAAO,IAAIF,aAAa,CAACG,QAAQ,CAACD,OAAO,CAACE,OAAO,CAAC;AAC3D,CAAC;AAED,MAAMC,mBAAmB,GAAIC,GAAa,IAAK;EAC7CC,KAAK,CAACC,IAAI,CAACF,GAAG,CAACG,gBAAgB,CAAC,IAAI,CAAC,CAAC,CACnCC,MAAM,CACJC,EAAE,IACDV,cAAc,CAACU,EAAE,CAACC,sBAAsB,CAAC,IACzCX,cAAc,CAACU,EAAE,CAACE,kBAAkB,CACxC,CAAC,CACAC,OAAO,CAAEH,EAAE,IAAK;IACfA,EAAE,CAACI,UAAU,EAAEC,WAAW,CAACL,EAAE,CAAC;EAChC,CAAC,CAAC;AACN,CAAC;AAED,MAAMM,mBAAmB,GAAIX,GAAa,IAAK;EAC7CC,KAAK,CAACC,IAAI,CAACF,GAAG,CAACG,gBAAgB,CAAC,yBAAyB,CAAC,CAAC,CACxDC,MAAM,CAAEQ,IAAI,IAAKA,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,EAAEC,KAAK,CAACrB,kBAAkB,CAAC,CAAC,CACvEe,OAAO,CAAEI,IAAI,IAAK;IACjB,MAAMG,QAAQ,GAAGf,GAAG,CAACgB,sBAAsB,CAAC,CAAC;IAC7CD,QAAQ,CAACE,MAAM,CAAC,GAAGL,IAAI,CAACM,UAAU,CAAC;IACnCN,IAAI,CAACH,UAAU,EAAEU,YAAY,CAACJ,QAAQ,EAAEH,IAAI,CAAC;EAC/C,CAAC,CAAC;AACN,CAAC;AAED,eAAe,SAASQ,SAASA,CAACpB,GAAa,EAAE;EAC/C,IAAIA,GAAG,CAACqB,aAAa,CAAC,6BAA6B,CAAC,EAAE;IACpDV,mBAAmB,CAACX,GAAG,CAAC;IACxBD,mBAAmB,CAACC,GAAG,CAAC;EAC1B;AACF", "ignoreList": []}