{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node scripts/seedAdmin.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "nodemailer": "^7.0.3", "prop-types": "^15.8.1"}, "devDependencies": {"nodemon": "^3.1.10"}}