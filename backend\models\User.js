const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  email: {
    type: String,
    required: true,
    unique: true
  },
  mobileNumber: {
    type: String,
    required: true
  },
  alternativeMobileNumber: {
    type: String
  },
  password: {
    type: String,
    required: true
  },
  role: {
    type: String,
    enum: ['user', 'admin'],
    default: 'user'
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  isSignedAgreement: {
    type: Boolean,
    default: false
  },
  signature: {
    type: String
  },
  otp: {
    code: String,
    expiresAt: Date
  },
  workStartedAt: {
    type: Date
  },
  workSubmitted: {
    type: Boolean,
    default: false
  },
  isPenalized: {
    type: Boolean,
    default: false
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('User', userSchema);