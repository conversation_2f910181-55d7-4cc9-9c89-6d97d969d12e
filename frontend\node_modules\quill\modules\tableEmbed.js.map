{"version": 3, "file": "tableEmbed.js", "names": ["Delta", "OpIterator", "<PERSON><PERSON><PERSON>", "parseCellIdentity", "identity", "parts", "split", "Number", "stringifyCellIdentity", "row", "column", "composePosition", "delta", "index", "newIndex", "thisIter", "ops", "offset", "hasNext", "length", "<PERSON><PERSON><PERSON><PERSON>", "nextType", "peekType", "next", "compactCellData", "_ref", "content", "attributes", "data", "Object", "keys", "compactTableData", "_ref2", "rows", "columns", "cells", "reindexCellIdentities", "_ref3", "reindexed<PERSON><PERSON><PERSON>", "for<PERSON>ach", "newPosition", "<PERSON><PERSON><PERSON><PERSON>", "compose", "a", "b", "keep<PERSON>ull", "aCell", "b<PERSON><PERSON>", "AttributeMap", "cell", "transform", "priority", "a<PERSON>eltas", "b<PERSON><PERSON><PERSON>", "newIdentity", "invert", "change", "base", "changeCell", "baseCell", "TableEmbed", "register", "registerEmbed"], "sources": ["../../src/modules/tableEmbed.ts"], "sourcesContent": ["import Delta, { OpIterator } from 'quill-delta';\nimport type { Op, AttributeMap } from 'quill-delta';\nimport Module from '../core/module.js';\n\nexport type CellData = {\n  content?: Delta['ops'];\n  attributes?: Record<string, unknown>;\n};\n\nexport type TableRowColumnOp = Omit<Op, 'insert'> & {\n  insert?: { id: string };\n};\n\nexport interface TableData {\n  rows?: Delta['ops'];\n  columns?: Delta['ops'];\n  cells?: Record<string, CellData>;\n}\n\nconst parseCellIdentity = (identity: string) => {\n  const parts = identity.split(':');\n  return [Number(parts[0]) - 1, Number(parts[1]) - 1];\n};\n\nconst stringifyCellIdentity = (row: number, column: number) =>\n  `${row + 1}:${column + 1}`;\n\nexport const composePosition = (delta: Delta, index: number) => {\n  let newIndex = index;\n  const thisIter = new OpIterator(delta.ops);\n  let offset = 0;\n  while (thisIter.hasNext() && offset <= newIndex) {\n    const length = thisIter.peekLength();\n    const nextType = thisIter.peekType();\n    thisIter.next();\n    switch (nextType) {\n      case 'delete':\n        if (length > newIndex - offset) {\n          return null;\n        }\n        newIndex -= length;\n        break;\n      case 'insert':\n        newIndex += length;\n        offset += length;\n        break;\n      default:\n        offset += length;\n        break;\n    }\n  }\n  return newIndex;\n};\n\nconst compactCellData = ({\n  content,\n  attributes,\n}: {\n  content: Delta;\n  attributes: AttributeMap | undefined;\n}) => {\n  const data: CellData = {};\n  if (content.length() > 0) {\n    data.content = content.ops;\n  }\n  if (attributes && Object.keys(attributes).length > 0) {\n    data.attributes = attributes;\n  }\n  return Object.keys(data).length > 0 ? data : null;\n};\n\nconst compactTableData = ({\n  rows,\n  columns,\n  cells,\n}: {\n  rows: Delta;\n  columns: Delta;\n  cells: Record<string, CellData>;\n}) => {\n  const data: TableData = {};\n  if (rows.length() > 0) {\n    data.rows = rows.ops;\n  }\n\n  if (columns.length() > 0) {\n    data.columns = columns.ops;\n  }\n\n  if (Object.keys(cells).length) {\n    data.cells = cells;\n  }\n\n  return data;\n};\n\nconst reindexCellIdentities = (\n  cells: Record<string, CellData>,\n  { rows, columns }: { rows: Delta; columns: Delta },\n) => {\n  const reindexedCells: Record<string, CellData> = {};\n  Object.keys(cells).forEach((identity) => {\n    let [row, column] = parseCellIdentity(identity);\n\n    // @ts-expect-error Fix me later\n    row = composePosition(rows, row);\n    // @ts-expect-error Fix me later\n    column = composePosition(columns, column);\n\n    if (row !== null && column !== null) {\n      const newPosition = stringifyCellIdentity(row, column);\n      reindexedCells[newPosition] = cells[identity];\n    }\n  }, false);\n  return reindexedCells;\n};\n\nexport const tableHandler = {\n  compose(a: TableData, b: TableData, keepNull?: boolean) {\n    const rows = new Delta(a.rows || []).compose(new Delta(b.rows || []));\n    const columns = new Delta(a.columns || []).compose(\n      new Delta(b.columns || []),\n    );\n\n    const cells = reindexCellIdentities(a.cells || {}, {\n      rows: new Delta(b.rows || []),\n      columns: new Delta(b.columns || []),\n    });\n\n    Object.keys(b.cells || {}).forEach((identity) => {\n      const aCell = cells[identity] || {};\n      // @ts-expect-error Fix me later\n      const bCell = b.cells[identity];\n\n      const content = new Delta(aCell.content || []).compose(\n        new Delta(bCell.content || []),\n      );\n\n      const attributes = Delta.AttributeMap.compose(\n        aCell.attributes,\n        bCell.attributes,\n        keepNull,\n      );\n\n      const cell = compactCellData({ content, attributes });\n      if (cell) {\n        cells[identity] = cell;\n      } else {\n        delete cells[identity];\n      }\n    });\n\n    return compactTableData({ rows, columns, cells });\n  },\n  transform(a: TableData, b: TableData, priority: boolean) {\n    const aDeltas = {\n      rows: new Delta(a.rows || []),\n      columns: new Delta(a.columns || []),\n    };\n\n    const bDeltas = {\n      rows: new Delta(b.rows || []),\n      columns: new Delta(b.columns || []),\n    };\n\n    const rows = aDeltas.rows.transform(bDeltas.rows, priority);\n    const columns = aDeltas.columns.transform(bDeltas.columns, priority);\n\n    const cells = reindexCellIdentities(b.cells || {}, {\n      rows: bDeltas.rows.transform(aDeltas.rows, !priority),\n      columns: bDeltas.columns.transform(aDeltas.columns, !priority),\n    });\n\n    Object.keys(a.cells || {}).forEach((identity) => {\n      let [row, column] = parseCellIdentity(identity);\n      // @ts-expect-error Fix me later\n      row = composePosition(rows, row);\n      // @ts-expect-error Fix me later\n      column = composePosition(columns, column);\n\n      if (row !== null && column !== null) {\n        const newIdentity = stringifyCellIdentity(row, column);\n\n        // @ts-expect-error Fix me later\n        const aCell = a.cells[identity];\n        const bCell = cells[newIdentity];\n        if (bCell) {\n          const content = new Delta(aCell.content || []).transform(\n            new Delta(bCell.content || []),\n            priority,\n          );\n\n          const attributes = Delta.AttributeMap.transform(\n            aCell.attributes,\n            bCell.attributes,\n            priority,\n          );\n\n          const cell = compactCellData({ content, attributes });\n          if (cell) {\n            cells[newIdentity] = cell;\n          } else {\n            delete cells[newIdentity];\n          }\n        }\n      }\n    });\n\n    return compactTableData({ rows, columns, cells });\n  },\n  invert(change: TableData, base: TableData) {\n    const rows = new Delta(change.rows || []).invert(\n      new Delta(base.rows || []),\n    );\n    const columns = new Delta(change.columns || []).invert(\n      new Delta(base.columns || []),\n    );\n    const cells = reindexCellIdentities(change.cells || {}, {\n      rows,\n      columns,\n    });\n    Object.keys(cells).forEach((identity) => {\n      const changeCell = cells[identity] || {};\n      const baseCell = (base.cells || {})[identity] || {};\n      const content = new Delta(changeCell.content || []).invert(\n        new Delta(baseCell.content || []),\n      );\n      const attributes = Delta.AttributeMap.invert(\n        changeCell.attributes,\n        baseCell.attributes,\n      );\n      const cell = compactCellData({ content, attributes });\n      if (cell) {\n        cells[identity] = cell;\n      } else {\n        delete cells[identity];\n      }\n    });\n\n    // Cells may be removed when their row or column is removed\n    // by row/column deltas. We should add them back.\n    Object.keys(base.cells || {}).forEach((identity) => {\n      const [row, column] = parseCellIdentity(identity);\n      if (\n        composePosition(new Delta(change.rows || []), row) === null ||\n        composePosition(new Delta(change.columns || []), column) === null\n      ) {\n        // @ts-expect-error Fix me later\n        cells[identity] = base.cells[identity];\n      }\n    });\n\n    return compactTableData({ rows, columns, cells });\n  },\n};\n\nclass TableEmbed extends Module {\n  static register() {\n    Delta.registerEmbed('table-embed', tableHandler);\n  }\n}\n\nexport default TableEmbed;\n"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,UAAU,QAAQ,aAAa;AAE/C,OAAOC,MAAM,MAAM,mBAAmB;AAiBtC,MAAMC,iBAAiB,GAAIC,QAAgB,IAAK;EAC9C,MAAMC,KAAK,GAAGD,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC;EACjC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEE,MAAM,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACrD,CAAC;AAED,MAAMG,qBAAqB,GAAGA,CAACC,GAAW,EAAEC,MAAc,KACvD,GAAED,GAAG,GAAG,CAAE,IAAGC,MAAM,GAAG,CAAE,EAAC;AAE5B,OAAO,MAAMC,eAAe,GAAGA,CAACC,KAAY,EAAEC,KAAa,KAAK;EAC9D,IAAIC,QAAQ,GAAGD,KAAK;EACpB,MAAME,QAAQ,GAAG,IAAId,UAAU,CAACW,KAAK,CAACI,GAAG,CAAC;EAC1C,IAAIC,MAAM,GAAG,CAAC;EACd,OAAOF,QAAQ,CAACG,OAAO,CAAC,CAAC,IAAID,MAAM,IAAIH,QAAQ,EAAE;IAC/C,MAAMK,MAAM,GAAGJ,QAAQ,CAACK,UAAU,CAAC,CAAC;IACpC,MAAMC,QAAQ,GAAGN,QAAQ,CAACO,QAAQ,CAAC,CAAC;IACpCP,QAAQ,CAACQ,IAAI,CAAC,CAAC;IACf,QAAQF,QAAQ;MACd,KAAK,QAAQ;QACX,IAAIF,MAAM,GAAGL,QAAQ,GAAGG,MAAM,EAAE;UAC9B,OAAO,IAAI;QACb;QACAH,QAAQ,IAAIK,MAAM;QAClB;MACF,KAAK,QAAQ;QACXL,QAAQ,IAAIK,MAAM;QAClBF,MAAM,IAAIE,MAAM;QAChB;MACF;QACEF,MAAM,IAAIE,MAAM;QAChB;IACJ;EACF;EACA,OAAOL,QAAQ;AACjB,CAAC;AAED,MAAMU,eAAe,GAAGC,IAAA,IAMlB;EAAA,IANmB;IACvBC,OAAO;IACPC;EAIF,CAAC,GAAAF,IAAA;EACC,MAAMG,IAAc,GAAG,CAAC,CAAC;EACzB,IAAIF,OAAO,CAACP,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE;IACxBS,IAAI,CAACF,OAAO,GAAGA,OAAO,CAACV,GAAG;EAC5B;EACA,IAAIW,UAAU,IAAIE,MAAM,CAACC,IAAI,CAACH,UAAU,CAAC,CAACR,MAAM,GAAG,CAAC,EAAE;IACpDS,IAAI,CAACD,UAAU,GAAGA,UAAU;EAC9B;EACA,OAAOE,MAAM,CAACC,IAAI,CAACF,IAAI,CAAC,CAACT,MAAM,GAAG,CAAC,GAAGS,IAAI,GAAG,IAAI;AACnD,CAAC;AAED,MAAMG,gBAAgB,GAAGC,KAAA,IAQnB;EAAA,IARoB;IACxBC,IAAI;IACJC,OAAO;IACPC;EAKF,CAAC,GAAAH,KAAA;EACC,MAAMJ,IAAe,GAAG,CAAC,CAAC;EAC1B,IAAIK,IAAI,CAACd,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE;IACrBS,IAAI,CAACK,IAAI,GAAGA,IAAI,CAACjB,GAAG;EACtB;EAEA,IAAIkB,OAAO,CAACf,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE;IACxBS,IAAI,CAACM,OAAO,GAAGA,OAAO,CAAClB,GAAG;EAC5B;EAEA,IAAIa,MAAM,CAACC,IAAI,CAACK,KAAK,CAAC,CAAChB,MAAM,EAAE;IAC7BS,IAAI,CAACO,KAAK,GAAGA,KAAK;EACpB;EAEA,OAAOP,IAAI;AACb,CAAC;AAED,MAAMQ,qBAAqB,GAAGA,CAC5BD,KAA+B,EAAAE,KAAA,KAE5B;EAAA,IADH;IAAEJ,IAAI;IAAEC;EAAyC,CAAC,GAAAG,KAAA;EAElD,MAAMC,cAAwC,GAAG,CAAC,CAAC;EACnDT,MAAM,CAACC,IAAI,CAACK,KAAK,CAAC,CAACI,OAAO,CAAEnC,QAAQ,IAAK;IACvC,IAAI,CAACK,GAAG,EAAEC,MAAM,CAAC,GAAGP,iBAAiB,CAACC,QAAQ,CAAC;;IAE/C;IACAK,GAAG,GAAGE,eAAe,CAACsB,IAAI,EAAExB,GAAG,CAAC;IAChC;IACAC,MAAM,GAAGC,eAAe,CAACuB,OAAO,EAAExB,MAAM,CAAC;IAEzC,IAAID,GAAG,KAAK,IAAI,IAAIC,MAAM,KAAK,IAAI,EAAE;MACnC,MAAM8B,WAAW,GAAGhC,qBAAqB,CAACC,GAAG,EAAEC,MAAM,CAAC;MACtD4B,cAAc,CAACE,WAAW,CAAC,GAAGL,KAAK,CAAC/B,QAAQ,CAAC;IAC/C;EACF,CAAC,EAAE,KAAK,CAAC;EACT,OAAOkC,cAAc;AACvB,CAAC;AAED,OAAO,MAAMG,YAAY,GAAG;EAC1BC,OAAOA,CAACC,CAAY,EAAEC,CAAY,EAAEC,QAAkB,EAAE;IACtD,MAAMZ,IAAI,GAAG,IAAIjC,KAAK,CAAC2C,CAAC,CAACV,IAAI,IAAI,EAAE,CAAC,CAACS,OAAO,CAAC,IAAI1C,KAAK,CAAC4C,CAAC,CAACX,IAAI,IAAI,EAAE,CAAC,CAAC;IACrE,MAAMC,OAAO,GAAG,IAAIlC,KAAK,CAAC2C,CAAC,CAACT,OAAO,IAAI,EAAE,CAAC,CAACQ,OAAO,CAChD,IAAI1C,KAAK,CAAC4C,CAAC,CAACV,OAAO,IAAI,EAAE,CAC3B,CAAC;IAED,MAAMC,KAAK,GAAGC,qBAAqB,CAACO,CAAC,CAACR,KAAK,IAAI,CAAC,CAAC,EAAE;MACjDF,IAAI,EAAE,IAAIjC,KAAK,CAAC4C,CAAC,CAACX,IAAI,IAAI,EAAE,CAAC;MAC7BC,OAAO,EAAE,IAAIlC,KAAK,CAAC4C,CAAC,CAACV,OAAO,IAAI,EAAE;IACpC,CAAC,CAAC;IAEFL,MAAM,CAACC,IAAI,CAACc,CAAC,CAACT,KAAK,IAAI,CAAC,CAAC,CAAC,CAACI,OAAO,CAAEnC,QAAQ,IAAK;MAC/C,MAAM0C,KAAK,GAAGX,KAAK,CAAC/B,QAAQ,CAAC,IAAI,CAAC,CAAC;MACnC;MACA,MAAM2C,KAAK,GAAGH,CAAC,CAACT,KAAK,CAAC/B,QAAQ,CAAC;MAE/B,MAAMsB,OAAO,GAAG,IAAI1B,KAAK,CAAC8C,KAAK,CAACpB,OAAO,IAAI,EAAE,CAAC,CAACgB,OAAO,CACpD,IAAI1C,KAAK,CAAC+C,KAAK,CAACrB,OAAO,IAAI,EAAE,CAC/B,CAAC;MAED,MAAMC,UAAU,GAAG3B,KAAK,CAACgD,YAAY,CAACN,OAAO,CAC3CI,KAAK,CAACnB,UAAU,EAChBoB,KAAK,CAACpB,UAAU,EAChBkB,QACF,CAAC;MAED,MAAMI,IAAI,GAAGzB,eAAe,CAAC;QAAEE,OAAO;QAAEC;MAAW,CAAC,CAAC;MACrD,IAAIsB,IAAI,EAAE;QACRd,KAAK,CAAC/B,QAAQ,CAAC,GAAG6C,IAAI;MACxB,CAAC,MAAM;QACL,OAAOd,KAAK,CAAC/B,QAAQ,CAAC;MACxB;IACF,CAAC,CAAC;IAEF,OAAO2B,gBAAgB,CAAC;MAAEE,IAAI;MAAEC,OAAO;MAAEC;IAAM,CAAC,CAAC;EACnD,CAAC;EACDe,SAASA,CAACP,CAAY,EAAEC,CAAY,EAAEO,QAAiB,EAAE;IACvD,MAAMC,OAAO,GAAG;MACdnB,IAAI,EAAE,IAAIjC,KAAK,CAAC2C,CAAC,CAACV,IAAI,IAAI,EAAE,CAAC;MAC7BC,OAAO,EAAE,IAAIlC,KAAK,CAAC2C,CAAC,CAACT,OAAO,IAAI,EAAE;IACpC,CAAC;IAED,MAAMmB,OAAO,GAAG;MACdpB,IAAI,EAAE,IAAIjC,KAAK,CAAC4C,CAAC,CAACX,IAAI,IAAI,EAAE,CAAC;MAC7BC,OAAO,EAAE,IAAIlC,KAAK,CAAC4C,CAAC,CAACV,OAAO,IAAI,EAAE;IACpC,CAAC;IAED,MAAMD,IAAI,GAAGmB,OAAO,CAACnB,IAAI,CAACiB,SAAS,CAACG,OAAO,CAACpB,IAAI,EAAEkB,QAAQ,CAAC;IAC3D,MAAMjB,OAAO,GAAGkB,OAAO,CAAClB,OAAO,CAACgB,SAAS,CAACG,OAAO,CAACnB,OAAO,EAAEiB,QAAQ,CAAC;IAEpE,MAAMhB,KAAK,GAAGC,qBAAqB,CAACQ,CAAC,CAACT,KAAK,IAAI,CAAC,CAAC,EAAE;MACjDF,IAAI,EAAEoB,OAAO,CAACpB,IAAI,CAACiB,SAAS,CAACE,OAAO,CAACnB,IAAI,EAAE,CAACkB,QAAQ,CAAC;MACrDjB,OAAO,EAAEmB,OAAO,CAACnB,OAAO,CAACgB,SAAS,CAACE,OAAO,CAAClB,OAAO,EAAE,CAACiB,QAAQ;IAC/D,CAAC,CAAC;IAEFtB,MAAM,CAACC,IAAI,CAACa,CAAC,CAACR,KAAK,IAAI,CAAC,CAAC,CAAC,CAACI,OAAO,CAAEnC,QAAQ,IAAK;MAC/C,IAAI,CAACK,GAAG,EAAEC,MAAM,CAAC,GAAGP,iBAAiB,CAACC,QAAQ,CAAC;MAC/C;MACAK,GAAG,GAAGE,eAAe,CAACsB,IAAI,EAAExB,GAAG,CAAC;MAChC;MACAC,MAAM,GAAGC,eAAe,CAACuB,OAAO,EAAExB,MAAM,CAAC;MAEzC,IAAID,GAAG,KAAK,IAAI,IAAIC,MAAM,KAAK,IAAI,EAAE;QACnC,MAAM4C,WAAW,GAAG9C,qBAAqB,CAACC,GAAG,EAAEC,MAAM,CAAC;;QAEtD;QACA,MAAMoC,KAAK,GAAGH,CAAC,CAACR,KAAK,CAAC/B,QAAQ,CAAC;QAC/B,MAAM2C,KAAK,GAAGZ,KAAK,CAACmB,WAAW,CAAC;QAChC,IAAIP,KAAK,EAAE;UACT,MAAMrB,OAAO,GAAG,IAAI1B,KAAK,CAAC8C,KAAK,CAACpB,OAAO,IAAI,EAAE,CAAC,CAACwB,SAAS,CACtD,IAAIlD,KAAK,CAAC+C,KAAK,CAACrB,OAAO,IAAI,EAAE,CAAC,EAC9ByB,QACF,CAAC;UAED,MAAMxB,UAAU,GAAG3B,KAAK,CAACgD,YAAY,CAACE,SAAS,CAC7CJ,KAAK,CAACnB,UAAU,EAChBoB,KAAK,CAACpB,UAAU,EAChBwB,QACF,CAAC;UAED,MAAMF,IAAI,GAAGzB,eAAe,CAAC;YAAEE,OAAO;YAAEC;UAAW,CAAC,CAAC;UACrD,IAAIsB,IAAI,EAAE;YACRd,KAAK,CAACmB,WAAW,CAAC,GAAGL,IAAI;UAC3B,CAAC,MAAM;YACL,OAAOd,KAAK,CAACmB,WAAW,CAAC;UAC3B;QACF;MACF;IACF,CAAC,CAAC;IAEF,OAAOvB,gBAAgB,CAAC;MAAEE,IAAI;MAAEC,OAAO;MAAEC;IAAM,CAAC,CAAC;EACnD,CAAC;EACDoB,MAAMA,CAACC,MAAiB,EAAEC,IAAe,EAAE;IACzC,MAAMxB,IAAI,GAAG,IAAIjC,KAAK,CAACwD,MAAM,CAACvB,IAAI,IAAI,EAAE,CAAC,CAACsB,MAAM,CAC9C,IAAIvD,KAAK,CAACyD,IAAI,CAACxB,IAAI,IAAI,EAAE,CAC3B,CAAC;IACD,MAAMC,OAAO,GAAG,IAAIlC,KAAK,CAACwD,MAAM,CAACtB,OAAO,IAAI,EAAE,CAAC,CAACqB,MAAM,CACpD,IAAIvD,KAAK,CAACyD,IAAI,CAACvB,OAAO,IAAI,EAAE,CAC9B,CAAC;IACD,MAAMC,KAAK,GAAGC,qBAAqB,CAACoB,MAAM,CAACrB,KAAK,IAAI,CAAC,CAAC,EAAE;MACtDF,IAAI;MACJC;IACF,CAAC,CAAC;IACFL,MAAM,CAACC,IAAI,CAACK,KAAK,CAAC,CAACI,OAAO,CAAEnC,QAAQ,IAAK;MACvC,MAAMsD,UAAU,GAAGvB,KAAK,CAAC/B,QAAQ,CAAC,IAAI,CAAC,CAAC;MACxC,MAAMuD,QAAQ,GAAG,CAACF,IAAI,CAACtB,KAAK,IAAI,CAAC,CAAC,EAAE/B,QAAQ,CAAC,IAAI,CAAC,CAAC;MACnD,MAAMsB,OAAO,GAAG,IAAI1B,KAAK,CAAC0D,UAAU,CAAChC,OAAO,IAAI,EAAE,CAAC,CAAC6B,MAAM,CACxD,IAAIvD,KAAK,CAAC2D,QAAQ,CAACjC,OAAO,IAAI,EAAE,CAClC,CAAC;MACD,MAAMC,UAAU,GAAG3B,KAAK,CAACgD,YAAY,CAACO,MAAM,CAC1CG,UAAU,CAAC/B,UAAU,EACrBgC,QAAQ,CAAChC,UACX,CAAC;MACD,MAAMsB,IAAI,GAAGzB,eAAe,CAAC;QAAEE,OAAO;QAAEC;MAAW,CAAC,CAAC;MACrD,IAAIsB,IAAI,EAAE;QACRd,KAAK,CAAC/B,QAAQ,CAAC,GAAG6C,IAAI;MACxB,CAAC,MAAM;QACL,OAAOd,KAAK,CAAC/B,QAAQ,CAAC;MACxB;IACF,CAAC,CAAC;;IAEF;IACA;IACAyB,MAAM,CAACC,IAAI,CAAC2B,IAAI,CAACtB,KAAK,IAAI,CAAC,CAAC,CAAC,CAACI,OAAO,CAAEnC,QAAQ,IAAK;MAClD,MAAM,CAACK,GAAG,EAAEC,MAAM,CAAC,GAAGP,iBAAiB,CAACC,QAAQ,CAAC;MACjD,IACEO,eAAe,CAAC,IAAIX,KAAK,CAACwD,MAAM,CAACvB,IAAI,IAAI,EAAE,CAAC,EAAExB,GAAG,CAAC,KAAK,IAAI,IAC3DE,eAAe,CAAC,IAAIX,KAAK,CAACwD,MAAM,CAACtB,OAAO,IAAI,EAAE,CAAC,EAAExB,MAAM,CAAC,KAAK,IAAI,EACjE;QACA;QACAyB,KAAK,CAAC/B,QAAQ,CAAC,GAAGqD,IAAI,CAACtB,KAAK,CAAC/B,QAAQ,CAAC;MACxC;IACF,CAAC,CAAC;IAEF,OAAO2B,gBAAgB,CAAC;MAAEE,IAAI;MAAEC,OAAO;MAAEC;IAAM,CAAC,CAAC;EACnD;AACF,CAAC;AAED,MAAMyB,UAAU,SAAS1D,MAAM,CAAC;EAC9B,OAAO2D,QAAQA,CAAA,EAAG;IAChB7D,KAAK,CAAC8D,aAAa,CAAC,aAAa,EAAErB,YAAY,CAAC;EAClD;AACF;AAEA,eAAemB,UAAU", "ignoreList": []}