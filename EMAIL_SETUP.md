# Email Configuration Setup

## Current Status
- **SKIP_EMAIL=true** is set in backend/.env for testing
- Registration works without email verification
- O<PERSON> is displayed in backend console for testing

## To Enable Real Email Sending:

### 1. Gmail App Password Setup
1. Go to your Google Account settings
2. Enable 2-Factor Authentication
3. Go to Security > App passwords
4. Generate a new app password for "Mail"
5. Copy the 16-character password

### 2. Update Backend .env File
```env
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_16_character_app_password
SKIP_EMAIL=false
```

### 3. Alternative Email Services
If Gmail doesn't work, you can use other services:

**Outlook/Hotmail:**
```env
EMAIL_SERVICE=hotmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_password
```

**Yahoo:**
```env
EMAIL_SERVICE=yahoo
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
```

### 4. Testing Email Configuration
1. Set SKIP_EMAIL=false in .env
2. Register a new user
3. Check backend console for email sending logs
4. Check your email inbox for OTP

## Current Testing Mode
- Register with any email
- Check backend console for OTP
- Use the displayed OTP for verification
- Email sending is bypassed for testing
