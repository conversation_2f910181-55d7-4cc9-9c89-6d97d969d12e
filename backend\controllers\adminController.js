const User = require('../models/User');
const Work = require('../models/Work');
const Agreement = require('../models/Agreement');
const nodemailer = require('nodemailer');
const jwt = require('jsonwebtoken');

// Email transporter setup
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS
  }
});

// Generate OTP
const generateOTP = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Send OTP via email
const sendOTP = async (email, otp) => {
  const mailOptions = {
    from: process.env.EMAIL_USER,
    to: email,
    subject: 'Email Verification OTP',
    html: `<p>Your OTP for email verification is: <strong>${otp}</strong></p>`
  };

  await transporter.sendMail(mailOptions);
};

// Send agreement link
const sendAgreementLink = async (email, token) => {
  const agreementLink = `${process.env.FRONTEND_URL}/sign-agreement/${token}`;

  const mailOptions = {
    from: process.env.EMAIL_USER,
    to: email,
    subject: 'Sign Agreement',
    html: `<p>Please sign the agreement by clicking on this link: <a href="${agreementLink}">Sign Agreement</a></p>`
  };

  await transporter.sendMail(mailOptions);
};

// Get all users
exports.getAllUsers = async (req, res) => {
  try {
    const users = await User.find().select('-password -otp').sort({ createdAt: -1 });

    // Get work data for each user
    const usersWithWork = await Promise.all(users.map(async (user) => {
      const work = await Work.findOne({ userId: user._id });
      return {
        ...user.toObject(),
        work: work || null
      };
    }));

    res.status(200).json({ users: usersWithWork });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Create user
exports.createUser = async (req, res) => {
  try {
    const { name, email, mobileNumber, alternativeMobileNumber, password, role } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ message: 'User already exists' });
    }

    // Generate OTP
    const otp = generateOTP();
    const otpExpiry = new Date();
    otpExpiry.setMinutes(otpExpiry.getMinutes() + 10);

    // Create new user
    const newUser = new User({
      name,
      email,
      mobileNumber,
      alternativeMobileNumber,
      password,
      role: role || 'user',
      otp: {
        code: otp,
        expiresAt: otpExpiry
      }
    });

    await newUser.save();

    // Send OTP
    await sendOTP(email, otp);

    res.status(201).json({
      message: 'User created successfully. OTP sent to email.',
      user: {
        id: newUser._id,
        name: newUser.name,
        email: newUser.email,
        role: newUser.role
      }
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Update user
exports.updateUser = async (req, res) => {
  try {
    const { userId } = req.params;
    const { name, email, mobileNumber, alternativeMobileNumber, role, isVerified, isSignedAgreement, isPenalized } = req.body;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Update user fields
    user.name = name || user.name;
    user.email = email || user.email;
    user.mobileNumber = mobileNumber || user.mobileNumber;
    user.alternativeMobileNumber = alternativeMobileNumber || user.alternativeMobileNumber;
    user.role = role || user.role;

    if (typeof isVerified === 'boolean') user.isVerified = isVerified;
    if (typeof isSignedAgreement === 'boolean') user.isSignedAgreement = isSignedAgreement;
    if (typeof isPenalized === 'boolean') user.isPenalized = isPenalized;

    await user.save();

    res.status(200).json({
      message: 'User updated successfully',
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        isVerified: user.isVerified,
        isSignedAgreement: user.isSignedAgreement,
        isPenalized: user.isPenalized
      }
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Delete user
exports.deleteUser = async (req, res) => {
  try {
    const { userId } = req.params;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Delete user's work as well
    await Work.deleteMany({ userId });
    await User.findByIdAndDelete(userId);

    res.status(200).json({ message: 'User deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Send OTP manually
exports.sendOTPManually = async (req, res) => {
  try {
    const { userId } = req.params;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Generate new OTP
    const otp = generateOTP();
    const otpExpiry = new Date();
    otpExpiry.setMinutes(otpExpiry.getMinutes() + 10);

    user.otp = {
      code: otp,
      expiresAt: otpExpiry
    };
    await user.save();

    // Send OTP
    await sendOTP(user.email, otp);

    res.status(200).json({ message: 'OTP sent successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Send agreement link manually
exports.sendAgreementLinkManually = async (req, res) => {
  try {
    const { userId } = req.params;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Generate token for agreement signing
    const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, { expiresIn: '1d' });

    // Send agreement link and OTP
    const otp = generateOTP();
    const otpExpiry = new Date();
    otpExpiry.setMinutes(otpExpiry.getMinutes() + 10);

    user.otp = {
      code: otp,
      expiresAt: otpExpiry
    };
    await user.save();

    await sendAgreementLink(user.email, token);
    await sendOTP(user.email, otp);

    res.status(200).json({ message: 'Agreement link and OTP sent successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get all agreements
exports.getAllAgreements = async (req, res) => {
  try {
    const agreements = await Agreement.find().sort({ version: -1 });
    res.status(200).json({ agreements });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Create new agreement
exports.createAgreement = async (req, res) => {
  try {
    const { content } = req.body;

    // Get the latest version number
    const latestAgreement = await Agreement.findOne().sort({ version: -1 });
    const newVersion = latestAgreement ? latestAgreement.version + 1 : 1;

    const newAgreement = new Agreement({
      content,
      version: newVersion
    });

    await newAgreement.save();

    res.status(201).json({
      message: 'Agreement created successfully',
      agreement: newAgreement
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Update agreement
exports.updateAgreement = async (req, res) => {
  try {
    const { agreementId } = req.params;
    const { content } = req.body;

    const agreement = await Agreement.findById(agreementId);
    if (!agreement) {
      return res.status(404).json({ message: 'Agreement not found' });
    }

    agreement.content = content;
    await agreement.save();

    res.status(200).json({
      message: 'Agreement updated successfully',
      agreement
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Delete agreement
exports.deleteAgreement = async (req, res) => {
  try {
    const { agreementId } = req.params;

    const agreement = await Agreement.findById(agreementId);
    if (!agreement) {
      return res.status(404).json({ message: 'Agreement not found' });
    }

    await Agreement.findByIdAndDelete(agreementId);

    res.status(200).json({ message: 'Agreement deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get admin dashboard stats
exports.getDashboardStats = async (req, res) => {
  try {
    const totalUsers = await User.countDocuments({ role: 'user' });
    const verifiedUsers = await User.countDocuments({ role: 'user', isVerified: true });
    const signedUsers = await User.countDocuments({ role: 'user', isSignedAgreement: true });
    const workingUsers = await User.countDocuments({ role: 'user', workStartedAt: { $exists: true }, workSubmitted: false });
    const submittedUsers = await User.countDocuments({ role: 'user', workSubmitted: true });
    const penalizedUsers = await User.countDocuments({ role: 'user', isPenalized: true });

    res.status(200).json({
      stats: {
        totalUsers,
        verifiedUsers,
        signedUsers,
        workingUsers,
        submittedUsers,
        penalizedUsers
      }
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};
