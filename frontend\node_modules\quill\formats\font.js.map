{"version": 3, "file": "font.js", "names": ["ClassAttributor", "<PERSON><PERSON>", "StyleAttributor", "config", "scope", "INLINE", "whitelist", "FontClass", "FontStyleAttributor", "value", "node", "replace", "FontStyle"], "sources": ["../../src/formats/font.ts"], "sourcesContent": ["import { ClassAttributor, Scope, StyleAttributor } from 'parchment';\n\nconst config = {\n  scope: Scope.INLINE,\n  whitelist: ['serif', 'monospace'],\n};\n\nconst FontClass = new ClassAttributor('font', 'ql-font', config);\n\nclass FontStyleAttributor extends StyleAttributor {\n  value(node: HTMLElement) {\n    return super.value(node).replace(/[\"']/g, '');\n  }\n}\n\nconst FontStyle = new FontStyleAttributor('font', 'font-family', config);\n\nexport { FontStyle, FontClass };\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,KAAK,EAAEC,eAAe,QAAQ,WAAW;AAEnE,MAAMC,MAAM,GAAG;EACbC,KAAK,EAAEH,KAAK,CAACI,MAAM;EACnBC,SAAS,EAAE,CAAC,OAAO,EAAE,WAAW;AAClC,CAAC;AAED,MAAMC,SAAS,GAAG,IAAIP,eAAe,CAAC,MAAM,EAAE,SAAS,EAAEG,MAAM,CAAC;AAEhE,MAAMK,mBAAmB,SAASN,eAAe,CAAC;EAChDO,KAAKA,CAACC,IAAiB,EAAE;IACvB,OAAO,KAAK,CAACD,KAAK,CAACC,IAAI,CAAC,CAACC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;EAC/C;AACF;AAEA,MAAMC,SAAS,GAAG,IAAIJ,mBAAmB,CAAC,MAAM,EAAE,aAAa,EAAEL,MAAM,CAAC;AAExE,SAASS,SAAS,EAAEL,SAAS", "ignoreList": []}