{"version": 3, "file": "keyboard.js", "names": ["cloneDeep", "isEqual", "Delta", "AttributeMap", "EmbedBlot", "<PERSON><PERSON>", "TextBlot", "<PERSON><PERSON><PERSON>", "logger", "<PERSON><PERSON><PERSON>", "debug", "SHORTKEY", "test", "navigator", "platform", "Keyboard", "match", "evt", "binding", "some", "key", "which", "constructor", "quill", "options", "bindings", "Object", "keys", "for<PERSON>ach", "name", "addBinding", "shift<PERSON>ey", "handleEnter", "metaKey", "ctrl<PERSON>ey", "altKey", "userAgent", "collapsed", "handleBackspace", "handleDelete", "prefix", "suffix", "handleDeleteRange", "offset", "listen", "keyBinding", "context", "arguments", "length", "undefined", "handler", "normalize", "warn", "Array", "isArray", "singleBinding", "push", "root", "addEventListener", "defaultPrevented", "isComposing", "keyCode", "concat", "matches", "filter", "blot", "find", "target", "scroll", "range", "getSelection", "hasFocus", "line", "getLine", "index", "leafStart", "offsetStart", "<PERSON><PERSON><PERSON><PERSON>", "leafEnd", "offsetEnd", "prefixText", "value", "slice", "suffixText", "cur<PERSON><PERSON><PERSON><PERSON>", "empty", "format", "getFormat", "event", "prevented", "every", "call", "preventDefault", "<PERSON><PERSON><PERSON><PERSON>", "formats", "delta", "retain", "delete", "prev", "isPrevLineEmpty", "statics", "blotName", "curFormats", "prevFormats", "diff", "formatDelta", "compose", "updateContents", "sources", "USER", "focus", "next", "nextFormats", "deleteRange", "lineFormats", "reduce", "query", "BLOCK", "insert", "setSelection", "SILENT", "defaultOptions", "bold", "makeFormatHandler", "italic", "underline", "indent", "outdent", "list", "makeCodeBlockHandler", "deleteText", "tab", "table", "history", "cutoff", "formatLine", "scrollSelectionIntoView", "header", "module", "getModule", "row", "cell", "getTable", "shift", "tableSide", "blockquote", "trim", "insertText", "numLines", "cur", "makeEmbedArrowHandler", "makeTableArrowHandler", "DEFAULTS", "_ref", "CodeBlock", "TAB", "lines", "getLines", "i", "insertAt", "domNode", "textContent", "startsWith", "deleteAt", "update", "where", "leaf", "<PERSON><PERSON><PERSON>", "up", "targetRow", "parent", "targetCell", "children", "head", "Math", "min", "targetLine", "_ref2", "firstFormats", "lastFormats", "_table", "default"], "sources": ["../../src/modules/keyboard.ts"], "sourcesContent": ["import { cloneDeep, isEqual } from 'lodash-es';\nimport Delta, { AttributeMap } from 'quill-delta';\nimport { EmbedBlot, Scope, TextBlot } from 'parchment';\nimport type { Blot, BlockBlot } from 'parchment';\nimport Quill from '../core/quill.js';\nimport logger from '../core/logger.js';\nimport Module from '../core/module.js';\nimport type { BlockEmbed } from '../blots/block.js';\nimport type { Range } from '../core/selection.js';\n\nconst debug = logger('quill:keyboard');\n\nconst SHORTKEY = /Mac/i.test(navigator.platform) ? 'metaKey' : 'ctrlKey';\n\nexport interface Context {\n  collapsed: boolean;\n  empty: boolean;\n  offset: number;\n  prefix: string;\n  suffix: string;\n  format: Record<string, unknown>;\n  event: KeyboardEvent;\n  line: BlockEmbed | BlockBlot;\n}\n\ninterface BindingObject\n  extends Partial<Omit<Context, 'prefix' | 'suffix' | 'format'>> {\n  key: number | string | string[];\n  shortKey?: boolean | null;\n  shiftKey?: boolean | null;\n  altKey?: boolean | null;\n  metaKey?: boolean | null;\n  ctrlKey?: boolean | null;\n  prefix?: RegExp;\n  suffix?: RegExp;\n  format?: Record<string, unknown> | string[];\n  handler?: (\n    this: { quill: Quill },\n    range: Range,\n    curContext: Context,\n    // eslint-disable-next-line no-use-before-define\n    binding: NormalizedBinding,\n  ) => boolean | void;\n}\n\ntype Binding = BindingObject | string | number;\n\ninterface NormalizedBinding extends Omit<BindingObject, 'key' | 'shortKey'> {\n  key: string | number;\n}\n\ninterface KeyboardOptions {\n  bindings: Record<string, Binding>;\n}\n\ninterface KeyboardOptions {\n  bindings: Record<string, Binding>;\n}\n\nclass Keyboard extends Module<KeyboardOptions> {\n  static DEFAULTS: KeyboardOptions;\n\n  static match(evt: KeyboardEvent, binding: BindingObject) {\n    if (\n      (['altKey', 'ctrlKey', 'metaKey', 'shiftKey'] as const).some((key) => {\n        return !!binding[key] !== evt[key] && binding[key] !== null;\n      })\n    ) {\n      return false;\n    }\n    return binding.key === evt.key || binding.key === evt.which;\n  }\n\n  bindings: Record<string, NormalizedBinding[]>;\n\n  constructor(quill: Quill, options: Partial<KeyboardOptions>) {\n    super(quill, options);\n    this.bindings = {};\n    // @ts-expect-error Fix me later\n    Object.keys(this.options.bindings).forEach((name) => {\n      // @ts-expect-error Fix me later\n      if (this.options.bindings[name]) {\n        // @ts-expect-error Fix me later\n        this.addBinding(this.options.bindings[name]);\n      }\n    });\n    this.addBinding({ key: 'Enter', shiftKey: null }, this.handleEnter);\n    this.addBinding(\n      { key: 'Enter', metaKey: null, ctrlKey: null, altKey: null },\n      () => {},\n    );\n    if (/Firefox/i.test(navigator.userAgent)) {\n      // Need to handle delete and backspace for Firefox in the general case #1171\n      this.addBinding(\n        { key: 'Backspace' },\n        { collapsed: true },\n        this.handleBackspace,\n      );\n      this.addBinding(\n        { key: 'Delete' },\n        { collapsed: true },\n        this.handleDelete,\n      );\n    } else {\n      this.addBinding(\n        { key: 'Backspace' },\n        { collapsed: true, prefix: /^.?$/ },\n        this.handleBackspace,\n      );\n      this.addBinding(\n        { key: 'Delete' },\n        { collapsed: true, suffix: /^.?$/ },\n        this.handleDelete,\n      );\n    }\n    this.addBinding(\n      { key: 'Backspace' },\n      { collapsed: false },\n      this.handleDeleteRange,\n    );\n    this.addBinding(\n      { key: 'Delete' },\n      { collapsed: false },\n      this.handleDeleteRange,\n    );\n    this.addBinding(\n      {\n        key: 'Backspace',\n        altKey: null,\n        ctrlKey: null,\n        metaKey: null,\n        shiftKey: null,\n      },\n      { collapsed: true, offset: 0 },\n      this.handleBackspace,\n    );\n    this.listen();\n  }\n\n  addBinding(\n    keyBinding: Binding,\n    context:\n      | Required<BindingObject['handler']>\n      | Partial<Omit<BindingObject, 'key' | 'handler'>> = {},\n    handler:\n      | Required<BindingObject['handler']>\n      | Partial<Omit<BindingObject, 'key' | 'handler'>> = {},\n  ) {\n    const binding = normalize(keyBinding);\n    if (binding == null) {\n      debug.warn('Attempted to add invalid keyboard binding', binding);\n      return;\n    }\n    if (typeof context === 'function') {\n      context = { handler: context };\n    }\n    if (typeof handler === 'function') {\n      handler = { handler };\n    }\n    const keys = Array.isArray(binding.key) ? binding.key : [binding.key];\n    keys.forEach((key) => {\n      const singleBinding = {\n        ...binding,\n        key,\n        ...context,\n        ...handler,\n      };\n      this.bindings[singleBinding.key] = this.bindings[singleBinding.key] || [];\n      this.bindings[singleBinding.key].push(singleBinding);\n    });\n  }\n\n  listen() {\n    this.quill.root.addEventListener('keydown', (evt) => {\n      if (evt.defaultPrevented || evt.isComposing) return;\n\n      // evt.isComposing is false when pressing Enter/Backspace when composing in Safari\n      // https://bugs.webkit.org/show_bug.cgi?id=165004\n      const isComposing =\n        evt.keyCode === 229 && (evt.key === 'Enter' || evt.key === 'Backspace');\n      if (isComposing) return;\n\n      const bindings = (this.bindings[evt.key] || []).concat(\n        this.bindings[evt.which] || [],\n      );\n      const matches = bindings.filter((binding) =>\n        Keyboard.match(evt, binding),\n      );\n      if (matches.length === 0) return;\n      // @ts-expect-error\n      const blot = Quill.find(evt.target, true);\n      if (blot && blot.scroll !== this.quill.scroll) return;\n      const range = this.quill.getSelection();\n      if (range == null || !this.quill.hasFocus()) return;\n      const [line, offset] = this.quill.getLine(range.index);\n      const [leafStart, offsetStart] = this.quill.getLeaf(range.index);\n      const [leafEnd, offsetEnd] =\n        range.length === 0\n          ? [leafStart, offsetStart]\n          : this.quill.getLeaf(range.index + range.length);\n      const prefixText =\n        leafStart instanceof TextBlot\n          ? leafStart.value().slice(0, offsetStart)\n          : '';\n      const suffixText =\n        leafEnd instanceof TextBlot ? leafEnd.value().slice(offsetEnd) : '';\n      const curContext = {\n        collapsed: range.length === 0,\n        // @ts-expect-error Fix me later\n        empty: range.length === 0 && line.length() <= 1,\n        format: this.quill.getFormat(range),\n        line,\n        offset,\n        prefix: prefixText,\n        suffix: suffixText,\n        event: evt,\n      };\n      const prevented = matches.some((binding) => {\n        if (\n          binding.collapsed != null &&\n          binding.collapsed !== curContext.collapsed\n        ) {\n          return false;\n        }\n        if (binding.empty != null && binding.empty !== curContext.empty) {\n          return false;\n        }\n        if (binding.offset != null && binding.offset !== curContext.offset) {\n          return false;\n        }\n        if (Array.isArray(binding.format)) {\n          // any format is present\n          if (binding.format.every((name) => curContext.format[name] == null)) {\n            return false;\n          }\n        } else if (typeof binding.format === 'object') {\n          // all formats must match\n          if (\n            !Object.keys(binding.format).every((name) => {\n              // @ts-expect-error Fix me later\n              if (binding.format[name] === true)\n                return curContext.format[name] != null;\n              // @ts-expect-error Fix me later\n              if (binding.format[name] === false)\n                return curContext.format[name] == null;\n              // @ts-expect-error Fix me later\n              return isEqual(binding.format[name], curContext.format[name]);\n            })\n          ) {\n            return false;\n          }\n        }\n        if (binding.prefix != null && !binding.prefix.test(curContext.prefix)) {\n          return false;\n        }\n        if (binding.suffix != null && !binding.suffix.test(curContext.suffix)) {\n          return false;\n        }\n        // @ts-expect-error Fix me later\n        return binding.handler.call(this, range, curContext, binding) !== true;\n      });\n      if (prevented) {\n        evt.preventDefault();\n      }\n    });\n  }\n\n  handleBackspace(range: Range, context: Context) {\n    // Check for astral symbols\n    const length = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]$/.test(context.prefix)\n      ? 2\n      : 1;\n    if (range.index === 0 || this.quill.getLength() <= 1) return;\n    let formats = {};\n    const [line] = this.quill.getLine(range.index);\n    let delta = new Delta().retain(range.index - length).delete(length);\n    if (context.offset === 0) {\n      // Always deleting newline here, length always 1\n      const [prev] = this.quill.getLine(range.index - 1);\n      if (prev) {\n        const isPrevLineEmpty =\n          prev.statics.blotName === 'block' && prev.length() <= 1;\n        if (!isPrevLineEmpty) {\n          // @ts-expect-error Fix me later\n          const curFormats = line.formats();\n          const prevFormats = this.quill.getFormat(range.index - 1, 1);\n          formats = AttributeMap.diff(curFormats, prevFormats) || {};\n          if (Object.keys(formats).length > 0) {\n            // line.length() - 1 targets \\n in line, another -1 for newline being deleted\n            const formatDelta = new Delta()\n              // @ts-expect-error Fix me later\n              .retain(range.index + line.length() - 2)\n              .retain(1, formats);\n            delta = delta.compose(formatDelta);\n          }\n        }\n      }\n    }\n    this.quill.updateContents(delta, Quill.sources.USER);\n    this.quill.focus();\n  }\n\n  handleDelete(range: Range, context: Context) {\n    // Check for astral symbols\n    const length = /^[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/.test(context.suffix)\n      ? 2\n      : 1;\n    if (range.index >= this.quill.getLength() - length) return;\n    let formats = {};\n    const [line] = this.quill.getLine(range.index);\n    let delta = new Delta().retain(range.index).delete(length);\n    // @ts-expect-error Fix me later\n    if (context.offset >= line.length() - 1) {\n      const [next] = this.quill.getLine(range.index + 1);\n      if (next) {\n        // @ts-expect-error Fix me later\n        const curFormats = line.formats();\n        const nextFormats = this.quill.getFormat(range.index, 1);\n        formats = AttributeMap.diff(curFormats, nextFormats) || {};\n        if (Object.keys(formats).length > 0) {\n          delta = delta.retain(next.length() - 1).retain(1, formats);\n        }\n      }\n    }\n    this.quill.updateContents(delta, Quill.sources.USER);\n    this.quill.focus();\n  }\n\n  handleDeleteRange(range: Range) {\n    deleteRange({ range, quill: this.quill });\n    this.quill.focus();\n  }\n\n  handleEnter(range: Range, context: Context) {\n    const lineFormats = Object.keys(context.format).reduce(\n      (formats: Record<string, unknown>, format) => {\n        if (\n          this.quill.scroll.query(format, Scope.BLOCK) &&\n          !Array.isArray(context.format[format])\n        ) {\n          formats[format] = context.format[format];\n        }\n        return formats;\n      },\n      {},\n    );\n    const delta = new Delta()\n      .retain(range.index)\n      .delete(range.length)\n      .insert('\\n', lineFormats);\n    this.quill.updateContents(delta, Quill.sources.USER);\n    this.quill.setSelection(range.index + 1, Quill.sources.SILENT);\n    this.quill.focus();\n  }\n}\n\nconst defaultOptions: KeyboardOptions = {\n  bindings: {\n    bold: makeFormatHandler('bold'),\n    italic: makeFormatHandler('italic'),\n    underline: makeFormatHandler('underline'),\n    indent: {\n      // highlight tab or tab at beginning of list, indent or blockquote\n      key: 'Tab',\n      format: ['blockquote', 'indent', 'list'],\n      handler(range, context) {\n        if (context.collapsed && context.offset !== 0) return true;\n        this.quill.format('indent', '+1', Quill.sources.USER);\n        return false;\n      },\n    },\n    outdent: {\n      key: 'Tab',\n      shiftKey: true,\n      format: ['blockquote', 'indent', 'list'],\n      // highlight tab or tab at beginning of list, indent or blockquote\n      handler(range, context) {\n        if (context.collapsed && context.offset !== 0) return true;\n        this.quill.format('indent', '-1', Quill.sources.USER);\n        return false;\n      },\n    },\n    'outdent backspace': {\n      key: 'Backspace',\n      collapsed: true,\n      shiftKey: null,\n      metaKey: null,\n      ctrlKey: null,\n      altKey: null,\n      format: ['indent', 'list'],\n      offset: 0,\n      handler(range, context) {\n        if (context.format.indent != null) {\n          this.quill.format('indent', '-1', Quill.sources.USER);\n        } else if (context.format.list != null) {\n          this.quill.format('list', false, Quill.sources.USER);\n        }\n      },\n    },\n    'indent code-block': makeCodeBlockHandler(true),\n    'outdent code-block': makeCodeBlockHandler(false),\n    'remove tab': {\n      key: 'Tab',\n      shiftKey: true,\n      collapsed: true,\n      prefix: /\\t$/,\n      handler(range) {\n        this.quill.deleteText(range.index - 1, 1, Quill.sources.USER);\n      },\n    },\n    tab: {\n      key: 'Tab',\n      handler(range, context) {\n        if (context.format.table) return true;\n        this.quill.history.cutoff();\n        const delta = new Delta()\n          .retain(range.index)\n          .delete(range.length)\n          .insert('\\t');\n        this.quill.updateContents(delta, Quill.sources.USER);\n        this.quill.history.cutoff();\n        this.quill.setSelection(range.index + 1, Quill.sources.SILENT);\n        return false;\n      },\n    },\n    'blockquote empty enter': {\n      key: 'Enter',\n      collapsed: true,\n      format: ['blockquote'],\n      empty: true,\n      handler() {\n        this.quill.format('blockquote', false, Quill.sources.USER);\n      },\n    },\n    'list empty enter': {\n      key: 'Enter',\n      collapsed: true,\n      format: ['list'],\n      empty: true,\n      handler(range, context) {\n        const formats: Record<string, unknown> = { list: false };\n        if (context.format.indent) {\n          formats.indent = false;\n        }\n        this.quill.formatLine(\n          range.index,\n          range.length,\n          formats,\n          Quill.sources.USER,\n        );\n      },\n    },\n    'checklist enter': {\n      key: 'Enter',\n      collapsed: true,\n      format: { list: 'checked' },\n      handler(range) {\n        const [line, offset] = this.quill.getLine(range.index);\n        const formats = {\n          // @ts-expect-error Fix me later\n          ...line.formats(),\n          list: 'checked',\n        };\n        const delta = new Delta()\n          .retain(range.index)\n          .insert('\\n', formats)\n          // @ts-expect-error Fix me later\n          .retain(line.length() - offset - 1)\n          .retain(1, { list: 'unchecked' });\n        this.quill.updateContents(delta, Quill.sources.USER);\n        this.quill.setSelection(range.index + 1, Quill.sources.SILENT);\n        this.quill.scrollSelectionIntoView();\n      },\n    },\n    'header enter': {\n      key: 'Enter',\n      collapsed: true,\n      format: ['header'],\n      suffix: /^$/,\n      handler(range, context) {\n        const [line, offset] = this.quill.getLine(range.index);\n        const delta = new Delta()\n          .retain(range.index)\n          .insert('\\n', context.format)\n          // @ts-expect-error Fix me later\n          .retain(line.length() - offset - 1)\n          .retain(1, { header: null });\n        this.quill.updateContents(delta, Quill.sources.USER);\n        this.quill.setSelection(range.index + 1, Quill.sources.SILENT);\n        this.quill.scrollSelectionIntoView();\n      },\n    },\n    'table backspace': {\n      key: 'Backspace',\n      format: ['table'],\n      collapsed: true,\n      offset: 0,\n      handler() {},\n    },\n    'table delete': {\n      key: 'Delete',\n      format: ['table'],\n      collapsed: true,\n      suffix: /^$/,\n      handler() {},\n    },\n    'table enter': {\n      key: 'Enter',\n      shiftKey: null,\n      format: ['table'],\n      handler(range) {\n        const module = this.quill.getModule('table');\n        if (module) {\n          // @ts-expect-error\n          const [table, row, cell, offset] = module.getTable(range);\n          const shift = tableSide(table, row, cell, offset);\n          if (shift == null) return;\n          let index = table.offset();\n          if (shift < 0) {\n            const delta = new Delta().retain(index).insert('\\n');\n            this.quill.updateContents(delta, Quill.sources.USER);\n            this.quill.setSelection(\n              range.index + 1,\n              range.length,\n              Quill.sources.SILENT,\n            );\n          } else if (shift > 0) {\n            index += table.length();\n            const delta = new Delta().retain(index).insert('\\n');\n            this.quill.updateContents(delta, Quill.sources.USER);\n            this.quill.setSelection(index, Quill.sources.USER);\n          }\n        }\n      },\n    },\n    'table tab': {\n      key: 'Tab',\n      shiftKey: null,\n      format: ['table'],\n      handler(range, context) {\n        const { event, line: cell } = context;\n        const offset = cell.offset(this.quill.scroll);\n        if (event.shiftKey) {\n          this.quill.setSelection(offset - 1, Quill.sources.USER);\n        } else {\n          this.quill.setSelection(offset + cell.length(), Quill.sources.USER);\n        }\n      },\n    },\n    'list autofill': {\n      key: ' ',\n      shiftKey: null,\n      collapsed: true,\n      format: {\n        'code-block': false,\n        blockquote: false,\n        table: false,\n      },\n      prefix: /^\\s*?(\\d+\\.|-|\\*|\\[ ?\\]|\\[x\\])$/,\n      handler(range, context) {\n        if (this.quill.scroll.query('list') == null) return true;\n        const { length } = context.prefix;\n        const [line, offset] = this.quill.getLine(range.index);\n        if (offset > length) return true;\n        let value;\n        switch (context.prefix.trim()) {\n          case '[]':\n          case '[ ]':\n            value = 'unchecked';\n            break;\n          case '[x]':\n            value = 'checked';\n            break;\n          case '-':\n          case '*':\n            value = 'bullet';\n            break;\n          default:\n            value = 'ordered';\n        }\n        this.quill.insertText(range.index, ' ', Quill.sources.USER);\n        this.quill.history.cutoff();\n        const delta = new Delta()\n          .retain(range.index - offset)\n          .delete(length + 1)\n          // @ts-expect-error Fix me later\n          .retain(line.length() - 2 - offset)\n          .retain(1, { list: value });\n        this.quill.updateContents(delta, Quill.sources.USER);\n        this.quill.history.cutoff();\n        this.quill.setSelection(range.index - length, Quill.sources.SILENT);\n        return false;\n      },\n    },\n    'code exit': {\n      key: 'Enter',\n      collapsed: true,\n      format: ['code-block'],\n      prefix: /^$/,\n      suffix: /^\\s*$/,\n      handler(range) {\n        const [line, offset] = this.quill.getLine(range.index);\n        let numLines = 2;\n        let cur = line;\n        while (\n          cur != null &&\n          cur.length() <= 1 &&\n          cur.formats()['code-block']\n        ) {\n          // @ts-expect-error\n          cur = cur.prev;\n          numLines -= 1;\n          // Requisite prev lines are empty\n          if (numLines <= 0) {\n            const delta = new Delta()\n              // @ts-expect-error Fix me later\n              .retain(range.index + line.length() - offset - 2)\n              .retain(1, { 'code-block': null })\n              .delete(1);\n            this.quill.updateContents(delta, Quill.sources.USER);\n            this.quill.setSelection(range.index - 1, Quill.sources.SILENT);\n            return false;\n          }\n        }\n        return true;\n      },\n    },\n    'embed left': makeEmbedArrowHandler('ArrowLeft', false),\n    'embed left shift': makeEmbedArrowHandler('ArrowLeft', true),\n    'embed right': makeEmbedArrowHandler('ArrowRight', false),\n    'embed right shift': makeEmbedArrowHandler('ArrowRight', true),\n    'table down': makeTableArrowHandler(false),\n    'table up': makeTableArrowHandler(true),\n  },\n};\n\nKeyboard.DEFAULTS = defaultOptions;\n\nfunction makeCodeBlockHandler(indent: boolean): BindingObject {\n  return {\n    key: 'Tab',\n    shiftKey: !indent,\n    format: { 'code-block': true },\n    handler(range, { event }) {\n      const CodeBlock = this.quill.scroll.query('code-block');\n      // @ts-expect-error\n      const { TAB } = CodeBlock;\n      if (range.length === 0 && !event.shiftKey) {\n        this.quill.insertText(range.index, TAB, Quill.sources.USER);\n        this.quill.setSelection(range.index + TAB.length, Quill.sources.SILENT);\n        return;\n      }\n\n      const lines =\n        range.length === 0\n          ? this.quill.getLines(range.index, 1)\n          : this.quill.getLines(range);\n      let { index, length } = range;\n      lines.forEach((line, i) => {\n        if (indent) {\n          line.insertAt(0, TAB);\n          if (i === 0) {\n            index += TAB.length;\n          } else {\n            length += TAB.length;\n          }\n          // @ts-expect-error Fix me later\n        } else if (line.domNode.textContent.startsWith(TAB)) {\n          line.deleteAt(0, TAB.length);\n          if (i === 0) {\n            index -= TAB.length;\n          } else {\n            length -= TAB.length;\n          }\n        }\n      });\n      this.quill.update(Quill.sources.USER);\n      this.quill.setSelection(index, length, Quill.sources.SILENT);\n    },\n  };\n}\n\nfunction makeEmbedArrowHandler(\n  key: string,\n  shiftKey: boolean | null,\n): BindingObject {\n  const where = key === 'ArrowLeft' ? 'prefix' : 'suffix';\n  return {\n    key,\n    shiftKey,\n    altKey: null,\n    [where]: /^$/,\n    handler(range) {\n      let { index } = range;\n      if (key === 'ArrowRight') {\n        index += range.length + 1;\n      }\n      const [leaf] = this.quill.getLeaf(index);\n      if (!(leaf instanceof EmbedBlot)) return true;\n      if (key === 'ArrowLeft') {\n        if (shiftKey) {\n          this.quill.setSelection(\n            range.index - 1,\n            range.length + 1,\n            Quill.sources.USER,\n          );\n        } else {\n          this.quill.setSelection(range.index - 1, Quill.sources.USER);\n        }\n      } else if (shiftKey) {\n        this.quill.setSelection(\n          range.index,\n          range.length + 1,\n          Quill.sources.USER,\n        );\n      } else {\n        this.quill.setSelection(\n          range.index + range.length + 1,\n          Quill.sources.USER,\n        );\n      }\n      return false;\n    },\n  };\n}\n\nfunction makeFormatHandler(format: string): BindingObject {\n  return {\n    key: format[0],\n    shortKey: true,\n    handler(range, context) {\n      this.quill.format(format, !context.format[format], Quill.sources.USER);\n    },\n  };\n}\n\nfunction makeTableArrowHandler(up: boolean): BindingObject {\n  return {\n    key: up ? 'ArrowUp' : 'ArrowDown',\n    collapsed: true,\n    format: ['table'],\n    handler(range, context) {\n      // TODO move to table module\n      const key = up ? 'prev' : 'next';\n      const cell = context.line;\n      const targetRow = cell.parent[key];\n      if (targetRow != null) {\n        if (targetRow.statics.blotName === 'table-row') {\n          // @ts-expect-error\n          let targetCell = targetRow.children.head;\n          let cur = cell;\n          while (cur.prev != null) {\n            // @ts-expect-error\n            cur = cur.prev;\n            targetCell = targetCell.next;\n          }\n          const index =\n            targetCell.offset(this.quill.scroll) +\n            Math.min(context.offset, targetCell.length() - 1);\n          this.quill.setSelection(index, 0, Quill.sources.USER);\n        }\n      } else {\n        // @ts-expect-error\n        const targetLine = cell.table()[key];\n        if (targetLine != null) {\n          if (up) {\n            this.quill.setSelection(\n              targetLine.offset(this.quill.scroll) + targetLine.length() - 1,\n              0,\n              Quill.sources.USER,\n            );\n          } else {\n            this.quill.setSelection(\n              targetLine.offset(this.quill.scroll),\n              0,\n              Quill.sources.USER,\n            );\n          }\n        }\n      }\n      return false;\n    },\n  };\n}\n\nfunction normalize(binding: Binding): BindingObject | null {\n  if (typeof binding === 'string' || typeof binding === 'number') {\n    binding = { key: binding };\n  } else if (typeof binding === 'object') {\n    binding = cloneDeep(binding);\n  } else {\n    return null;\n  }\n  if (binding.shortKey) {\n    binding[SHORTKEY] = binding.shortKey;\n    delete binding.shortKey;\n  }\n  return binding;\n}\n\n// TODO: Move into quill.ts or editor.ts\nfunction deleteRange({ quill, range }: { quill: Quill; range: Range }) {\n  const lines = quill.getLines(range);\n  let formats = {};\n  if (lines.length > 1) {\n    const firstFormats = lines[0].formats();\n    const lastFormats = lines[lines.length - 1].formats();\n    formats = AttributeMap.diff(lastFormats, firstFormats) || {};\n  }\n  quill.deleteText(range, Quill.sources.USER);\n  if (Object.keys(formats).length > 0) {\n    quill.formatLine(range.index, 1, formats, Quill.sources.USER);\n  }\n  quill.setSelection(range.index, Quill.sources.SILENT);\n}\n\nfunction tableSide(_table: unknown, row: Blot, cell: Blot, offset: number) {\n  if (row.prev == null && row.next == null) {\n    if (cell.prev == null && cell.next == null) {\n      return offset === 0 ? -1 : 1;\n    }\n    return cell.prev == null ? -1 : 1;\n  }\n  if (row.prev == null) {\n    return -1;\n  }\n  if (row.next == null) {\n    return 1;\n  }\n  return null;\n}\n\nexport { Keyboard as default, SHORTKEY, normalize, deleteRange };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,OAAO,QAAQ,WAAW;AAC9C,OAAOC,KAAK,IAAIC,YAAY,QAAQ,aAAa;AACjD,SAASC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,WAAW;AAEtD,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,mBAAmB;AAItC,MAAMC,KAAK,GAAGF,MAAM,CAAC,gBAAgB,CAAC;AAEtC,MAAMG,QAAQ,GAAG,MAAM,CAACC,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAC,GAAG,SAAS,GAAG,SAAS;AA+CxE,MAAMC,QAAQ,SAASN,MAAM,CAAkB;EAG7C,OAAOO,KAAKA,CAACC,GAAkB,EAAEC,OAAsB,EAAE;IACvD,IACG,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAWC,IAAI,CAAEC,GAAG,IAAK;MACpE,OAAO,CAAC,CAACF,OAAO,CAACE,GAAG,CAAC,KAAKH,GAAG,CAACG,GAAG,CAAC,IAAIF,OAAO,CAACE,GAAG,CAAC,KAAK,IAAI;IAC7D,CAAC,CAAC,EACF;MACA,OAAO,KAAK;IACd;IACA,OAAOF,OAAO,CAACE,GAAG,KAAKH,GAAG,CAACG,GAAG,IAAIF,OAAO,CAACE,GAAG,KAAKH,GAAG,CAACI,KAAK;EAC7D;EAIAC,WAAWA,CAACC,KAAY,EAAEC,OAAiC,EAAE;IAC3D,KAAK,CAACD,KAAK,EAAEC,OAAO,CAAC;IACrB,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;IAClB;IACAC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACH,OAAO,CAACC,QAAQ,CAAC,CAACG,OAAO,CAAEC,IAAI,IAAK;MACnD;MACA,IAAI,IAAI,CAACL,OAAO,CAACC,QAAQ,CAACI,IAAI,CAAC,EAAE;QAC/B;QACA,IAAI,CAACC,UAAU,CAAC,IAAI,CAACN,OAAO,CAACC,QAAQ,CAACI,IAAI,CAAC,CAAC;MAC9C;IACF,CAAC,CAAC;IACF,IAAI,CAACC,UAAU,CAAC;MAAEV,GAAG,EAAE,OAAO;MAAEW,QAAQ,EAAE;IAAK,CAAC,EAAE,IAAI,CAACC,WAAW,CAAC;IACnE,IAAI,CAACF,UAAU,CACb;MAAEV,GAAG,EAAE,OAAO;MAAEa,OAAO,EAAE,IAAI;MAAEC,OAAO,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAK,CAAC,EAC5D,MAAM,CAAC,CACT,CAAC;IACD,IAAI,UAAU,CAACvB,IAAI,CAACC,SAAS,CAACuB,SAAS,CAAC,EAAE;MACxC;MACA,IAAI,CAACN,UAAU,CACb;QAAEV,GAAG,EAAE;MAAY,CAAC,EACpB;QAAEiB,SAAS,EAAE;MAAK,CAAC,EACnB,IAAI,CAACC,eACP,CAAC;MACD,IAAI,CAACR,UAAU,CACb;QAAEV,GAAG,EAAE;MAAS,CAAC,EACjB;QAAEiB,SAAS,EAAE;MAAK,CAAC,EACnB,IAAI,CAACE,YACP,CAAC;IACH,CAAC,MAAM;MACL,IAAI,CAACT,UAAU,CACb;QAAEV,GAAG,EAAE;MAAY,CAAC,EACpB;QAAEiB,SAAS,EAAE,IAAI;QAAEG,MAAM,EAAE;MAAO,CAAC,EACnC,IAAI,CAACF,eACP,CAAC;MACD,IAAI,CAACR,UAAU,CACb;QAAEV,GAAG,EAAE;MAAS,CAAC,EACjB;QAAEiB,SAAS,EAAE,IAAI;QAAEI,MAAM,EAAE;MAAO,CAAC,EACnC,IAAI,CAACF,YACP,CAAC;IACH;IACA,IAAI,CAACT,UAAU,CACb;MAAEV,GAAG,EAAE;IAAY,CAAC,EACpB;MAAEiB,SAAS,EAAE;IAAM,CAAC,EACpB,IAAI,CAACK,iBACP,CAAC;IACD,IAAI,CAACZ,UAAU,CACb;MAAEV,GAAG,EAAE;IAAS,CAAC,EACjB;MAAEiB,SAAS,EAAE;IAAM,CAAC,EACpB,IAAI,CAACK,iBACP,CAAC;IACD,IAAI,CAACZ,UAAU,CACb;MACEV,GAAG,EAAE,WAAW;MAChBe,MAAM,EAAE,IAAI;MACZD,OAAO,EAAE,IAAI;MACbD,OAAO,EAAE,IAAI;MACbF,QAAQ,EAAE;IACZ,CAAC,EACD;MAAEM,SAAS,EAAE,IAAI;MAAEM,MAAM,EAAE;IAAE,CAAC,EAC9B,IAAI,CAACL,eACP,CAAC;IACD,IAAI,CAACM,MAAM,CAAC,CAAC;EACf;EAEAd,UAAUA,CACRe,UAAmB,EAOnB;IAAA,IANAC,OAEmD,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAAA,IACxDG,OAEmD,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAExD,MAAM7B,OAAO,GAAGiC,SAAS,CAACN,UAAU,CAAC;IACrC,IAAI3B,OAAO,IAAI,IAAI,EAAE;MACnBR,KAAK,CAAC0C,IAAI,CAAC,2CAA2C,EAAElC,OAAO,CAAC;MAChE;IACF;IACA,IAAI,OAAO4B,OAAO,KAAK,UAAU,EAAE;MACjCA,OAAO,GAAG;QAAEI,OAAO,EAAEJ;MAAQ,CAAC;IAChC;IACA,IAAI,OAAOI,OAAO,KAAK,UAAU,EAAE;MACjCA,OAAO,GAAG;QAAEA;MAAQ,CAAC;IACvB;IACA,MAAMvB,IAAI,GAAG0B,KAAK,CAACC,OAAO,CAACpC,OAAO,CAACE,GAAG,CAAC,GAAGF,OAAO,CAACE,GAAG,GAAG,CAACF,OAAO,CAACE,GAAG,CAAC;IACrEO,IAAI,CAACC,OAAO,CAAER,GAAG,IAAK;MACpB,MAAMmC,aAAa,GAAG;QACpB,GAAGrC,OAAO;QACVE,GAAG;QACH,GAAG0B,OAAO;QACV,GAAGI;MACL,CAAC;MACD,IAAI,CAACzB,QAAQ,CAAC8B,aAAa,CAACnC,GAAG,CAAC,GAAG,IAAI,CAACK,QAAQ,CAAC8B,aAAa,CAACnC,GAAG,CAAC,IAAI,EAAE;MACzE,IAAI,CAACK,QAAQ,CAAC8B,aAAa,CAACnC,GAAG,CAAC,CAACoC,IAAI,CAACD,aAAa,CAAC;IACtD,CAAC,CAAC;EACJ;EAEAX,MAAMA,CAAA,EAAG;IACP,IAAI,CAACrB,KAAK,CAACkC,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAGzC,GAAG,IAAK;MACnD,IAAIA,GAAG,CAAC0C,gBAAgB,IAAI1C,GAAG,CAAC2C,WAAW,EAAE;;MAE7C;MACA;MACA,MAAMA,WAAW,GACf3C,GAAG,CAAC4C,OAAO,KAAK,GAAG,KAAK5C,GAAG,CAACG,GAAG,KAAK,OAAO,IAAIH,GAAG,CAACG,GAAG,KAAK,WAAW,CAAC;MACzE,IAAIwC,WAAW,EAAE;MAEjB,MAAMnC,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ,CAACR,GAAG,CAACG,GAAG,CAAC,IAAI,EAAE,EAAE0C,MAAM,CACpD,IAAI,CAACrC,QAAQ,CAACR,GAAG,CAACI,KAAK,CAAC,IAAI,EAC9B,CAAC;MACD,MAAM0C,OAAO,GAAGtC,QAAQ,CAACuC,MAAM,CAAE9C,OAAO,IACtCH,QAAQ,CAACC,KAAK,CAACC,GAAG,EAAEC,OAAO,CAC7B,CAAC;MACD,IAAI6C,OAAO,CAACf,MAAM,KAAK,CAAC,EAAE;MAC1B;MACA,MAAMiB,IAAI,GAAG1D,KAAK,CAAC2D,IAAI,CAACjD,GAAG,CAACkD,MAAM,EAAE,IAAI,CAAC;MACzC,IAAIF,IAAI,IAAIA,IAAI,CAACG,MAAM,KAAK,IAAI,CAAC7C,KAAK,CAAC6C,MAAM,EAAE;MAC/C,MAAMC,KAAK,GAAG,IAAI,CAAC9C,KAAK,CAAC+C,YAAY,CAAC,CAAC;MACvC,IAAID,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC9C,KAAK,CAACgD,QAAQ,CAAC,CAAC,EAAE;MAC7C,MAAM,CAACC,IAAI,EAAE7B,MAAM,CAAC,GAAG,IAAI,CAACpB,KAAK,CAACkD,OAAO,CAACJ,KAAK,CAACK,KAAK,CAAC;MACtD,MAAM,CAACC,SAAS,EAAEC,WAAW,CAAC,GAAG,IAAI,CAACrD,KAAK,CAACsD,OAAO,CAACR,KAAK,CAACK,KAAK,CAAC;MAChE,MAAM,CAACI,OAAO,EAAEC,SAAS,CAAC,GACxBV,KAAK,CAACrB,MAAM,KAAK,CAAC,GACd,CAAC2B,SAAS,EAAEC,WAAW,CAAC,GACxB,IAAI,CAACrD,KAAK,CAACsD,OAAO,CAACR,KAAK,CAACK,KAAK,GAAGL,KAAK,CAACrB,MAAM,CAAC;MACpD,MAAMgC,UAAU,GACdL,SAAS,YAAYrE,QAAQ,GACzBqE,SAAS,CAACM,KAAK,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAEN,WAAW,CAAC,GACvC,EAAE;MACR,MAAMO,UAAU,GACdL,OAAO,YAAYxE,QAAQ,GAAGwE,OAAO,CAACG,KAAK,CAAC,CAAC,CAACC,KAAK,CAACH,SAAS,CAAC,GAAG,EAAE;MACrE,MAAMK,UAAU,GAAG;QACjB/C,SAAS,EAAEgC,KAAK,CAACrB,MAAM,KAAK,CAAC;QAC7B;QACAqC,KAAK,EAAEhB,KAAK,CAACrB,MAAM,KAAK,CAAC,IAAIwB,IAAI,CAACxB,MAAM,CAAC,CAAC,IAAI,CAAC;QAC/CsC,MAAM,EAAE,IAAI,CAAC/D,KAAK,CAACgE,SAAS,CAAClB,KAAK,CAAC;QACnCG,IAAI;QACJ7B,MAAM;QACNH,MAAM,EAAEwC,UAAU;QAClBvC,MAAM,EAAE0C,UAAU;QAClBK,KAAK,EAAEvE;MACT,CAAC;MACD,MAAMwE,SAAS,GAAG1B,OAAO,CAAC5C,IAAI,CAAED,OAAO,IAAK;QAC1C,IACEA,OAAO,CAACmB,SAAS,IAAI,IAAI,IACzBnB,OAAO,CAACmB,SAAS,KAAK+C,UAAU,CAAC/C,SAAS,EAC1C;UACA,OAAO,KAAK;QACd;QACA,IAAInB,OAAO,CAACmE,KAAK,IAAI,IAAI,IAAInE,OAAO,CAACmE,KAAK,KAAKD,UAAU,CAACC,KAAK,EAAE;UAC/D,OAAO,KAAK;QACd;QACA,IAAInE,OAAO,CAACyB,MAAM,IAAI,IAAI,IAAIzB,OAAO,CAACyB,MAAM,KAAKyC,UAAU,CAACzC,MAAM,EAAE;UAClE,OAAO,KAAK;QACd;QACA,IAAIU,KAAK,CAACC,OAAO,CAACpC,OAAO,CAACoE,MAAM,CAAC,EAAE;UACjC;UACA,IAAIpE,OAAO,CAACoE,MAAM,CAACI,KAAK,CAAE7D,IAAI,IAAKuD,UAAU,CAACE,MAAM,CAACzD,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE;YACnE,OAAO,KAAK;UACd;QACF,CAAC,MAAM,IAAI,OAAOX,OAAO,CAACoE,MAAM,KAAK,QAAQ,EAAE;UAC7C;UACA,IACE,CAAC5D,MAAM,CAACC,IAAI,CAACT,OAAO,CAACoE,MAAM,CAAC,CAACI,KAAK,CAAE7D,IAAI,IAAK;YAC3C;YACA,IAAIX,OAAO,CAACoE,MAAM,CAACzD,IAAI,CAAC,KAAK,IAAI,EAC/B,OAAOuD,UAAU,CAACE,MAAM,CAACzD,IAAI,CAAC,IAAI,IAAI;YACxC;YACA,IAAIX,OAAO,CAACoE,MAAM,CAACzD,IAAI,CAAC,KAAK,KAAK,EAChC,OAAOuD,UAAU,CAACE,MAAM,CAACzD,IAAI,CAAC,IAAI,IAAI;YACxC;YACA,OAAO5B,OAAO,CAACiB,OAAO,CAACoE,MAAM,CAACzD,IAAI,CAAC,EAAEuD,UAAU,CAACE,MAAM,CAACzD,IAAI,CAAC,CAAC;UAC/D,CAAC,CAAC,EACF;YACA,OAAO,KAAK;UACd;QACF;QACA,IAAIX,OAAO,CAACsB,MAAM,IAAI,IAAI,IAAI,CAACtB,OAAO,CAACsB,MAAM,CAAC5B,IAAI,CAACwE,UAAU,CAAC5C,MAAM,CAAC,EAAE;UACrE,OAAO,KAAK;QACd;QACA,IAAItB,OAAO,CAACuB,MAAM,IAAI,IAAI,IAAI,CAACvB,OAAO,CAACuB,MAAM,CAAC7B,IAAI,CAACwE,UAAU,CAAC3C,MAAM,CAAC,EAAE;UACrE,OAAO,KAAK;QACd;QACA;QACA,OAAOvB,OAAO,CAACgC,OAAO,CAACyC,IAAI,CAAC,IAAI,EAAEtB,KAAK,EAAEe,UAAU,EAAElE,OAAO,CAAC,KAAK,IAAI;MACxE,CAAC,CAAC;MACF,IAAIuE,SAAS,EAAE;QACbxE,GAAG,CAAC2E,cAAc,CAAC,CAAC;MACtB;IACF,CAAC,CAAC;EACJ;EAEAtD,eAAeA,CAAC+B,KAAY,EAAEvB,OAAgB,EAAE;IAC9C;IACA,MAAME,MAAM,GAAG,iCAAiC,CAACpC,IAAI,CAACkC,OAAO,CAACN,MAAM,CAAC,GACjE,CAAC,GACD,CAAC;IACL,IAAI6B,KAAK,CAACK,KAAK,KAAK,CAAC,IAAI,IAAI,CAACnD,KAAK,CAACsE,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE;IACtD,IAAIC,OAAO,GAAG,CAAC,CAAC;IAChB,MAAM,CAACtB,IAAI,CAAC,GAAG,IAAI,CAACjD,KAAK,CAACkD,OAAO,CAACJ,KAAK,CAACK,KAAK,CAAC;IAC9C,IAAIqB,KAAK,GAAG,IAAI7F,KAAK,CAAC,CAAC,CAAC8F,MAAM,CAAC3B,KAAK,CAACK,KAAK,GAAG1B,MAAM,CAAC,CAACiD,MAAM,CAACjD,MAAM,CAAC;IACnE,IAAIF,OAAO,CAACH,MAAM,KAAK,CAAC,EAAE;MACxB;MACA,MAAM,CAACuD,IAAI,CAAC,GAAG,IAAI,CAAC3E,KAAK,CAACkD,OAAO,CAACJ,KAAK,CAACK,KAAK,GAAG,CAAC,CAAC;MAClD,IAAIwB,IAAI,EAAE;QACR,MAAMC,eAAe,GACnBD,IAAI,CAACE,OAAO,CAACC,QAAQ,KAAK,OAAO,IAAIH,IAAI,CAAClD,MAAM,CAAC,CAAC,IAAI,CAAC;QACzD,IAAI,CAACmD,eAAe,EAAE;UACpB;UACA,MAAMG,UAAU,GAAG9B,IAAI,CAACsB,OAAO,CAAC,CAAC;UACjC,MAAMS,WAAW,GAAG,IAAI,CAAChF,KAAK,CAACgE,SAAS,CAAClB,KAAK,CAACK,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;UAC5DoB,OAAO,GAAG3F,YAAY,CAACqG,IAAI,CAACF,UAAU,EAAEC,WAAW,CAAC,IAAI,CAAC,CAAC;UAC1D,IAAI7E,MAAM,CAACC,IAAI,CAACmE,OAAO,CAAC,CAAC9C,MAAM,GAAG,CAAC,EAAE;YACnC;YACA,MAAMyD,WAAW,GAAG,IAAIvG,KAAK,CAAC;YAC5B;YAAA,CACC8F,MAAM,CAAC3B,KAAK,CAACK,KAAK,GAAGF,IAAI,CAACxB,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CACvCgD,MAAM,CAAC,CAAC,EAAEF,OAAO,CAAC;YACrBC,KAAK,GAAGA,KAAK,CAACW,OAAO,CAACD,WAAW,CAAC;UACpC;QACF;MACF;IACF;IACA,IAAI,CAAClF,KAAK,CAACoF,cAAc,CAACZ,KAAK,EAAExF,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;IACpD,IAAI,CAACtF,KAAK,CAACuF,KAAK,CAAC,CAAC;EACpB;EAEAvE,YAAYA,CAAC8B,KAAY,EAAEvB,OAAgB,EAAE;IAC3C;IACA,MAAME,MAAM,GAAG,iCAAiC,CAACpC,IAAI,CAACkC,OAAO,CAACL,MAAM,CAAC,GACjE,CAAC,GACD,CAAC;IACL,IAAI4B,KAAK,CAACK,KAAK,IAAI,IAAI,CAACnD,KAAK,CAACsE,SAAS,CAAC,CAAC,GAAG7C,MAAM,EAAE;IACpD,IAAI8C,OAAO,GAAG,CAAC,CAAC;IAChB,MAAM,CAACtB,IAAI,CAAC,GAAG,IAAI,CAACjD,KAAK,CAACkD,OAAO,CAACJ,KAAK,CAACK,KAAK,CAAC;IAC9C,IAAIqB,KAAK,GAAG,IAAI7F,KAAK,CAAC,CAAC,CAAC8F,MAAM,CAAC3B,KAAK,CAACK,KAAK,CAAC,CAACuB,MAAM,CAACjD,MAAM,CAAC;IAC1D;IACA,IAAIF,OAAO,CAACH,MAAM,IAAI6B,IAAI,CAACxB,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE;MACvC,MAAM,CAAC+D,IAAI,CAAC,GAAG,IAAI,CAACxF,KAAK,CAACkD,OAAO,CAACJ,KAAK,CAACK,KAAK,GAAG,CAAC,CAAC;MAClD,IAAIqC,IAAI,EAAE;QACR;QACA,MAAMT,UAAU,GAAG9B,IAAI,CAACsB,OAAO,CAAC,CAAC;QACjC,MAAMkB,WAAW,GAAG,IAAI,CAACzF,KAAK,CAACgE,SAAS,CAAClB,KAAK,CAACK,KAAK,EAAE,CAAC,CAAC;QACxDoB,OAAO,GAAG3F,YAAY,CAACqG,IAAI,CAACF,UAAU,EAAEU,WAAW,CAAC,IAAI,CAAC,CAAC;QAC1D,IAAItF,MAAM,CAACC,IAAI,CAACmE,OAAO,CAAC,CAAC9C,MAAM,GAAG,CAAC,EAAE;UACnC+C,KAAK,GAAGA,KAAK,CAACC,MAAM,CAACe,IAAI,CAAC/D,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAACgD,MAAM,CAAC,CAAC,EAAEF,OAAO,CAAC;QAC5D;MACF;IACF;IACA,IAAI,CAACvE,KAAK,CAACoF,cAAc,CAACZ,KAAK,EAAExF,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;IACpD,IAAI,CAACtF,KAAK,CAACuF,KAAK,CAAC,CAAC;EACpB;EAEApE,iBAAiBA,CAAC2B,KAAY,EAAE;IAC9B4C,WAAW,CAAC;MAAE5C,KAAK;MAAE9C,KAAK,EAAE,IAAI,CAACA;IAAM,CAAC,CAAC;IACzC,IAAI,CAACA,KAAK,CAACuF,KAAK,CAAC,CAAC;EACpB;EAEA9E,WAAWA,CAACqC,KAAY,EAAEvB,OAAgB,EAAE;IAC1C,MAAMoE,WAAW,GAAGxF,MAAM,CAACC,IAAI,CAACmB,OAAO,CAACwC,MAAM,CAAC,CAAC6B,MAAM,CACpD,CAACrB,OAAgC,EAAER,MAAM,KAAK;MAC5C,IACE,IAAI,CAAC/D,KAAK,CAAC6C,MAAM,CAACgD,KAAK,CAAC9B,MAAM,EAAEjF,KAAK,CAACgH,KAAK,CAAC,IAC5C,CAAChE,KAAK,CAACC,OAAO,CAACR,OAAO,CAACwC,MAAM,CAACA,MAAM,CAAC,CAAC,EACtC;QACAQ,OAAO,CAACR,MAAM,CAAC,GAAGxC,OAAO,CAACwC,MAAM,CAACA,MAAM,CAAC;MAC1C;MACA,OAAOQ,OAAO;IAChB,CAAC,EACD,CAAC,CACH,CAAC;IACD,MAAMC,KAAK,GAAG,IAAI7F,KAAK,CAAC,CAAC,CACtB8F,MAAM,CAAC3B,KAAK,CAACK,KAAK,CAAC,CACnBuB,MAAM,CAAC5B,KAAK,CAACrB,MAAM,CAAC,CACpBsE,MAAM,CAAC,IAAI,EAAEJ,WAAW,CAAC;IAC5B,IAAI,CAAC3F,KAAK,CAACoF,cAAc,CAACZ,KAAK,EAAExF,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;IACpD,IAAI,CAACtF,KAAK,CAACgG,YAAY,CAAClD,KAAK,CAACK,KAAK,GAAG,CAAC,EAAEnE,KAAK,CAACqG,OAAO,CAACY,MAAM,CAAC;IAC9D,IAAI,CAACjG,KAAK,CAACuF,KAAK,CAAC,CAAC;EACpB;AACF;AAEA,MAAMW,cAA+B,GAAG;EACtChG,QAAQ,EAAE;IACRiG,IAAI,EAAEC,iBAAiB,CAAC,MAAM,CAAC;IAC/BC,MAAM,EAAED,iBAAiB,CAAC,QAAQ,CAAC;IACnCE,SAAS,EAAEF,iBAAiB,CAAC,WAAW,CAAC;IACzCG,MAAM,EAAE;MACN;MACA1G,GAAG,EAAE,KAAK;MACVkE,MAAM,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,MAAM,CAAC;MACxCpC,OAAOA,CAACmB,KAAK,EAAEvB,OAAO,EAAE;QACtB,IAAIA,OAAO,CAACT,SAAS,IAAIS,OAAO,CAACH,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;QAC1D,IAAI,CAACpB,KAAK,CAAC+D,MAAM,CAAC,QAAQ,EAAE,IAAI,EAAE/E,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;QACrD,OAAO,KAAK;MACd;IACF,CAAC;IACDkB,OAAO,EAAE;MACP3G,GAAG,EAAE,KAAK;MACVW,QAAQ,EAAE,IAAI;MACduD,MAAM,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,MAAM,CAAC;MACxC;MACApC,OAAOA,CAACmB,KAAK,EAAEvB,OAAO,EAAE;QACtB,IAAIA,OAAO,CAACT,SAAS,IAAIS,OAAO,CAACH,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;QAC1D,IAAI,CAACpB,KAAK,CAAC+D,MAAM,CAAC,QAAQ,EAAE,IAAI,EAAE/E,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;QACrD,OAAO,KAAK;MACd;IACF,CAAC;IACD,mBAAmB,EAAE;MACnBzF,GAAG,EAAE,WAAW;MAChBiB,SAAS,EAAE,IAAI;MACfN,QAAQ,EAAE,IAAI;MACdE,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE,IAAI;MACbC,MAAM,EAAE,IAAI;MACZmD,MAAM,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;MAC1B3C,MAAM,EAAE,CAAC;MACTO,OAAOA,CAACmB,KAAK,EAAEvB,OAAO,EAAE;QACtB,IAAIA,OAAO,CAACwC,MAAM,CAACwC,MAAM,IAAI,IAAI,EAAE;UACjC,IAAI,CAACvG,KAAK,CAAC+D,MAAM,CAAC,QAAQ,EAAE,IAAI,EAAE/E,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;QACvD,CAAC,MAAM,IAAI/D,OAAO,CAACwC,MAAM,CAAC0C,IAAI,IAAI,IAAI,EAAE;UACtC,IAAI,CAACzG,KAAK,CAAC+D,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE/E,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;QACtD;MACF;IACF,CAAC;IACD,mBAAmB,EAAEoB,oBAAoB,CAAC,IAAI,CAAC;IAC/C,oBAAoB,EAAEA,oBAAoB,CAAC,KAAK,CAAC;IACjD,YAAY,EAAE;MACZ7G,GAAG,EAAE,KAAK;MACVW,QAAQ,EAAE,IAAI;MACdM,SAAS,EAAE,IAAI;MACfG,MAAM,EAAE,KAAK;MACbU,OAAOA,CAACmB,KAAK,EAAE;QACb,IAAI,CAAC9C,KAAK,CAAC2G,UAAU,CAAC7D,KAAK,CAACK,KAAK,GAAG,CAAC,EAAE,CAAC,EAAEnE,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;MAC/D;IACF,CAAC;IACDsB,GAAG,EAAE;MACH/G,GAAG,EAAE,KAAK;MACV8B,OAAOA,CAACmB,KAAK,EAAEvB,OAAO,EAAE;QACtB,IAAIA,OAAO,CAACwC,MAAM,CAAC8C,KAAK,EAAE,OAAO,IAAI;QACrC,IAAI,CAAC7G,KAAK,CAAC8G,OAAO,CAACC,MAAM,CAAC,CAAC;QAC3B,MAAMvC,KAAK,GAAG,IAAI7F,KAAK,CAAC,CAAC,CACtB8F,MAAM,CAAC3B,KAAK,CAACK,KAAK,CAAC,CACnBuB,MAAM,CAAC5B,KAAK,CAACrB,MAAM,CAAC,CACpBsE,MAAM,CAAC,IAAI,CAAC;QACf,IAAI,CAAC/F,KAAK,CAACoF,cAAc,CAACZ,KAAK,EAAExF,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;QACpD,IAAI,CAACtF,KAAK,CAAC8G,OAAO,CAACC,MAAM,CAAC,CAAC;QAC3B,IAAI,CAAC/G,KAAK,CAACgG,YAAY,CAAClD,KAAK,CAACK,KAAK,GAAG,CAAC,EAAEnE,KAAK,CAACqG,OAAO,CAACY,MAAM,CAAC;QAC9D,OAAO,KAAK;MACd;IACF,CAAC;IACD,wBAAwB,EAAE;MACxBpG,GAAG,EAAE,OAAO;MACZiB,SAAS,EAAE,IAAI;MACfiD,MAAM,EAAE,CAAC,YAAY,CAAC;MACtBD,KAAK,EAAE,IAAI;MACXnC,OAAOA,CAAA,EAAG;QACR,IAAI,CAAC3B,KAAK,CAAC+D,MAAM,CAAC,YAAY,EAAE,KAAK,EAAE/E,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;MAC5D;IACF,CAAC;IACD,kBAAkB,EAAE;MAClBzF,GAAG,EAAE,OAAO;MACZiB,SAAS,EAAE,IAAI;MACfiD,MAAM,EAAE,CAAC,MAAM,CAAC;MAChBD,KAAK,EAAE,IAAI;MACXnC,OAAOA,CAACmB,KAAK,EAAEvB,OAAO,EAAE;QACtB,MAAMgD,OAAgC,GAAG;UAAEkC,IAAI,EAAE;QAAM,CAAC;QACxD,IAAIlF,OAAO,CAACwC,MAAM,CAACwC,MAAM,EAAE;UACzBhC,OAAO,CAACgC,MAAM,GAAG,KAAK;QACxB;QACA,IAAI,CAACvG,KAAK,CAACgH,UAAU,CACnBlE,KAAK,CAACK,KAAK,EACXL,KAAK,CAACrB,MAAM,EACZ8C,OAAO,EACPvF,KAAK,CAACqG,OAAO,CAACC,IAChB,CAAC;MACH;IACF,CAAC;IACD,iBAAiB,EAAE;MACjBzF,GAAG,EAAE,OAAO;MACZiB,SAAS,EAAE,IAAI;MACfiD,MAAM,EAAE;QAAE0C,IAAI,EAAE;MAAU,CAAC;MAC3B9E,OAAOA,CAACmB,KAAK,EAAE;QACb,MAAM,CAACG,IAAI,EAAE7B,MAAM,CAAC,GAAG,IAAI,CAACpB,KAAK,CAACkD,OAAO,CAACJ,KAAK,CAACK,KAAK,CAAC;QACtD,MAAMoB,OAAO,GAAG;UACd;UACA,GAAGtB,IAAI,CAACsB,OAAO,CAAC,CAAC;UACjBkC,IAAI,EAAE;QACR,CAAC;QACD,MAAMjC,KAAK,GAAG,IAAI7F,KAAK,CAAC,CAAC,CACtB8F,MAAM,CAAC3B,KAAK,CAACK,KAAK,CAAC,CACnB4C,MAAM,CAAC,IAAI,EAAExB,OAAO;QACrB;QAAA,CACCE,MAAM,CAACxB,IAAI,CAACxB,MAAM,CAAC,CAAC,GAAGL,MAAM,GAAG,CAAC,CAAC,CAClCqD,MAAM,CAAC,CAAC,EAAE;UAAEgC,IAAI,EAAE;QAAY,CAAC,CAAC;QACnC,IAAI,CAACzG,KAAK,CAACoF,cAAc,CAACZ,KAAK,EAAExF,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;QACpD,IAAI,CAACtF,KAAK,CAACgG,YAAY,CAAClD,KAAK,CAACK,KAAK,GAAG,CAAC,EAAEnE,KAAK,CAACqG,OAAO,CAACY,MAAM,CAAC;QAC9D,IAAI,CAACjG,KAAK,CAACiH,uBAAuB,CAAC,CAAC;MACtC;IACF,CAAC;IACD,cAAc,EAAE;MACdpH,GAAG,EAAE,OAAO;MACZiB,SAAS,EAAE,IAAI;MACfiD,MAAM,EAAE,CAAC,QAAQ,CAAC;MAClB7C,MAAM,EAAE,IAAI;MACZS,OAAOA,CAACmB,KAAK,EAAEvB,OAAO,EAAE;QACtB,MAAM,CAAC0B,IAAI,EAAE7B,MAAM,CAAC,GAAG,IAAI,CAACpB,KAAK,CAACkD,OAAO,CAACJ,KAAK,CAACK,KAAK,CAAC;QACtD,MAAMqB,KAAK,GAAG,IAAI7F,KAAK,CAAC,CAAC,CACtB8F,MAAM,CAAC3B,KAAK,CAACK,KAAK,CAAC,CACnB4C,MAAM,CAAC,IAAI,EAAExE,OAAO,CAACwC,MAAM;QAC5B;QAAA,CACCU,MAAM,CAACxB,IAAI,CAACxB,MAAM,CAAC,CAAC,GAAGL,MAAM,GAAG,CAAC,CAAC,CAClCqD,MAAM,CAAC,CAAC,EAAE;UAAEyC,MAAM,EAAE;QAAK,CAAC,CAAC;QAC9B,IAAI,CAAClH,KAAK,CAACoF,cAAc,CAACZ,KAAK,EAAExF,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;QACpD,IAAI,CAACtF,KAAK,CAACgG,YAAY,CAAClD,KAAK,CAACK,KAAK,GAAG,CAAC,EAAEnE,KAAK,CAACqG,OAAO,CAACY,MAAM,CAAC;QAC9D,IAAI,CAACjG,KAAK,CAACiH,uBAAuB,CAAC,CAAC;MACtC;IACF,CAAC;IACD,iBAAiB,EAAE;MACjBpH,GAAG,EAAE,WAAW;MAChBkE,MAAM,EAAE,CAAC,OAAO,CAAC;MACjBjD,SAAS,EAAE,IAAI;MACfM,MAAM,EAAE,CAAC;MACTO,OAAOA,CAAA,EAAG,CAAC;IACb,CAAC;IACD,cAAc,EAAE;MACd9B,GAAG,EAAE,QAAQ;MACbkE,MAAM,EAAE,CAAC,OAAO,CAAC;MACjBjD,SAAS,EAAE,IAAI;MACfI,MAAM,EAAE,IAAI;MACZS,OAAOA,CAAA,EAAG,CAAC;IACb,CAAC;IACD,aAAa,EAAE;MACb9B,GAAG,EAAE,OAAO;MACZW,QAAQ,EAAE,IAAI;MACduD,MAAM,EAAE,CAAC,OAAO,CAAC;MACjBpC,OAAOA,CAACmB,KAAK,EAAE;QACb,MAAMqE,MAAM,GAAG,IAAI,CAACnH,KAAK,CAACoH,SAAS,CAAC,OAAO,CAAC;QAC5C,IAAID,MAAM,EAAE;UACV;UACA,MAAM,CAACN,KAAK,EAAEQ,GAAG,EAAEC,IAAI,EAAElG,MAAM,CAAC,GAAG+F,MAAM,CAACI,QAAQ,CAACzE,KAAK,CAAC;UACzD,MAAM0E,KAAK,GAAGC,SAAS,CAACZ,KAAK,EAAEQ,GAAG,EAAEC,IAAI,EAAElG,MAAM,CAAC;UACjD,IAAIoG,KAAK,IAAI,IAAI,EAAE;UACnB,IAAIrE,KAAK,GAAG0D,KAAK,CAACzF,MAAM,CAAC,CAAC;UAC1B,IAAIoG,KAAK,GAAG,CAAC,EAAE;YACb,MAAMhD,KAAK,GAAG,IAAI7F,KAAK,CAAC,CAAC,CAAC8F,MAAM,CAACtB,KAAK,CAAC,CAAC4C,MAAM,CAAC,IAAI,CAAC;YACpD,IAAI,CAAC/F,KAAK,CAACoF,cAAc,CAACZ,KAAK,EAAExF,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;YACpD,IAAI,CAACtF,KAAK,CAACgG,YAAY,CACrBlD,KAAK,CAACK,KAAK,GAAG,CAAC,EACfL,KAAK,CAACrB,MAAM,EACZzC,KAAK,CAACqG,OAAO,CAACY,MAChB,CAAC;UACH,CAAC,MAAM,IAAIuB,KAAK,GAAG,CAAC,EAAE;YACpBrE,KAAK,IAAI0D,KAAK,CAACpF,MAAM,CAAC,CAAC;YACvB,MAAM+C,KAAK,GAAG,IAAI7F,KAAK,CAAC,CAAC,CAAC8F,MAAM,CAACtB,KAAK,CAAC,CAAC4C,MAAM,CAAC,IAAI,CAAC;YACpD,IAAI,CAAC/F,KAAK,CAACoF,cAAc,CAACZ,KAAK,EAAExF,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;YACpD,IAAI,CAACtF,KAAK,CAACgG,YAAY,CAAC7C,KAAK,EAAEnE,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;UACpD;QACF;MACF;IACF,CAAC;IACD,WAAW,EAAE;MACXzF,GAAG,EAAE,KAAK;MACVW,QAAQ,EAAE,IAAI;MACduD,MAAM,EAAE,CAAC,OAAO,CAAC;MACjBpC,OAAOA,CAACmB,KAAK,EAAEvB,OAAO,EAAE;QACtB,MAAM;UAAE0C,KAAK;UAAEhB,IAAI,EAAEqE;QAAK,CAAC,GAAG/F,OAAO;QACrC,MAAMH,MAAM,GAAGkG,IAAI,CAAClG,MAAM,CAAC,IAAI,CAACpB,KAAK,CAAC6C,MAAM,CAAC;QAC7C,IAAIoB,KAAK,CAACzD,QAAQ,EAAE;UAClB,IAAI,CAACR,KAAK,CAACgG,YAAY,CAAC5E,MAAM,GAAG,CAAC,EAAEpC,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;QACzD,CAAC,MAAM;UACL,IAAI,CAACtF,KAAK,CAACgG,YAAY,CAAC5E,MAAM,GAAGkG,IAAI,CAAC7F,MAAM,CAAC,CAAC,EAAEzC,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;QACrE;MACF;IACF,CAAC;IACD,eAAe,EAAE;MACfzF,GAAG,EAAE,GAAG;MACRW,QAAQ,EAAE,IAAI;MACdM,SAAS,EAAE,IAAI;MACfiD,MAAM,EAAE;QACN,YAAY,EAAE,KAAK;QACnB2D,UAAU,EAAE,KAAK;QACjBb,KAAK,EAAE;MACT,CAAC;MACD5F,MAAM,EAAE,iCAAiC;MACzCU,OAAOA,CAACmB,KAAK,EAAEvB,OAAO,EAAE;QACtB,IAAI,IAAI,CAACvB,KAAK,CAAC6C,MAAM,CAACgD,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,OAAO,IAAI;QACxD,MAAM;UAAEpE;QAAO,CAAC,GAAGF,OAAO,CAACN,MAAM;QACjC,MAAM,CAACgC,IAAI,EAAE7B,MAAM,CAAC,GAAG,IAAI,CAACpB,KAAK,CAACkD,OAAO,CAACJ,KAAK,CAACK,KAAK,CAAC;QACtD,IAAI/B,MAAM,GAAGK,MAAM,EAAE,OAAO,IAAI;QAChC,IAAIiC,KAAK;QACT,QAAQnC,OAAO,CAACN,MAAM,CAAC0G,IAAI,CAAC,CAAC;UAC3B,KAAK,IAAI;UACT,KAAK,KAAK;YACRjE,KAAK,GAAG,WAAW;YACnB;UACF,KAAK,KAAK;YACRA,KAAK,GAAG,SAAS;YACjB;UACF,KAAK,GAAG;UACR,KAAK,GAAG;YACNA,KAAK,GAAG,QAAQ;YAChB;UACF;YACEA,KAAK,GAAG,SAAS;QACrB;QACA,IAAI,CAAC1D,KAAK,CAAC4H,UAAU,CAAC9E,KAAK,CAACK,KAAK,EAAE,GAAG,EAAEnE,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;QAC3D,IAAI,CAACtF,KAAK,CAAC8G,OAAO,CAACC,MAAM,CAAC,CAAC;QAC3B,MAAMvC,KAAK,GAAG,IAAI7F,KAAK,CAAC,CAAC,CACtB8F,MAAM,CAAC3B,KAAK,CAACK,KAAK,GAAG/B,MAAM,CAAC,CAC5BsD,MAAM,CAACjD,MAAM,GAAG,CAAC;QAClB;QAAA,CACCgD,MAAM,CAACxB,IAAI,CAACxB,MAAM,CAAC,CAAC,GAAG,CAAC,GAAGL,MAAM,CAAC,CAClCqD,MAAM,CAAC,CAAC,EAAE;UAAEgC,IAAI,EAAE/C;QAAM,CAAC,CAAC;QAC7B,IAAI,CAAC1D,KAAK,CAACoF,cAAc,CAACZ,KAAK,EAAExF,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;QACpD,IAAI,CAACtF,KAAK,CAAC8G,OAAO,CAACC,MAAM,CAAC,CAAC;QAC3B,IAAI,CAAC/G,KAAK,CAACgG,YAAY,CAAClD,KAAK,CAACK,KAAK,GAAG1B,MAAM,EAAEzC,KAAK,CAACqG,OAAO,CAACY,MAAM,CAAC;QACnE,OAAO,KAAK;MACd;IACF,CAAC;IACD,WAAW,EAAE;MACXpG,GAAG,EAAE,OAAO;MACZiB,SAAS,EAAE,IAAI;MACfiD,MAAM,EAAE,CAAC,YAAY,CAAC;MACtB9C,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,OAAO;MACfS,OAAOA,CAACmB,KAAK,EAAE;QACb,MAAM,CAACG,IAAI,EAAE7B,MAAM,CAAC,GAAG,IAAI,CAACpB,KAAK,CAACkD,OAAO,CAACJ,KAAK,CAACK,KAAK,CAAC;QACtD,IAAI0E,QAAQ,GAAG,CAAC;QAChB,IAAIC,GAAG,GAAG7E,IAAI;QACd,OACE6E,GAAG,IAAI,IAAI,IACXA,GAAG,CAACrG,MAAM,CAAC,CAAC,IAAI,CAAC,IACjBqG,GAAG,CAACvD,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,EAC3B;UACA;UACAuD,GAAG,GAAGA,GAAG,CAACnD,IAAI;UACdkD,QAAQ,IAAI,CAAC;UACb;UACA,IAAIA,QAAQ,IAAI,CAAC,EAAE;YACjB,MAAMrD,KAAK,GAAG,IAAI7F,KAAK,CAAC;YACtB;YAAA,CACC8F,MAAM,CAAC3B,KAAK,CAACK,KAAK,GAAGF,IAAI,CAACxB,MAAM,CAAC,CAAC,GAAGL,MAAM,GAAG,CAAC,CAAC,CAChDqD,MAAM,CAAC,CAAC,EAAE;cAAE,YAAY,EAAE;YAAK,CAAC,CAAC,CACjCC,MAAM,CAAC,CAAC,CAAC;YACZ,IAAI,CAAC1E,KAAK,CAACoF,cAAc,CAACZ,KAAK,EAAExF,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;YACpD,IAAI,CAACtF,KAAK,CAACgG,YAAY,CAAClD,KAAK,CAACK,KAAK,GAAG,CAAC,EAAEnE,KAAK,CAACqG,OAAO,CAACY,MAAM,CAAC;YAC9D,OAAO,KAAK;UACd;QACF;QACA,OAAO,IAAI;MACb;IACF,CAAC;IACD,YAAY,EAAE8B,qBAAqB,CAAC,WAAW,EAAE,KAAK,CAAC;IACvD,kBAAkB,EAAEA,qBAAqB,CAAC,WAAW,EAAE,IAAI,CAAC;IAC5D,aAAa,EAAEA,qBAAqB,CAAC,YAAY,EAAE,KAAK,CAAC;IACzD,mBAAmB,EAAEA,qBAAqB,CAAC,YAAY,EAAE,IAAI,CAAC;IAC9D,YAAY,EAAEC,qBAAqB,CAAC,KAAK,CAAC;IAC1C,UAAU,EAAEA,qBAAqB,CAAC,IAAI;EACxC;AACF,CAAC;AAEDxI,QAAQ,CAACyI,QAAQ,GAAG/B,cAAc;AAElC,SAASQ,oBAAoBA,CAACH,MAAe,EAAiB;EAC5D,OAAO;IACL1G,GAAG,EAAE,KAAK;IACVW,QAAQ,EAAE,CAAC+F,MAAM;IACjBxC,MAAM,EAAE;MAAE,YAAY,EAAE;IAAK,CAAC;IAC9BpC,OAAOA,CAACmB,KAAK,EAAAoF,IAAA,EAAa;MAAA,IAAX;QAAEjE;MAAM,CAAC,GAAAiE,IAAA;MACtB,MAAMC,SAAS,GAAG,IAAI,CAACnI,KAAK,CAAC6C,MAAM,CAACgD,KAAK,CAAC,YAAY,CAAC;MACvD;MACA,MAAM;QAAEuC;MAAI,CAAC,GAAGD,SAAS;MACzB,IAAIrF,KAAK,CAACrB,MAAM,KAAK,CAAC,IAAI,CAACwC,KAAK,CAACzD,QAAQ,EAAE;QACzC,IAAI,CAACR,KAAK,CAAC4H,UAAU,CAAC9E,KAAK,CAACK,KAAK,EAAEiF,GAAG,EAAEpJ,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;QAC3D,IAAI,CAACtF,KAAK,CAACgG,YAAY,CAAClD,KAAK,CAACK,KAAK,GAAGiF,GAAG,CAAC3G,MAAM,EAAEzC,KAAK,CAACqG,OAAO,CAACY,MAAM,CAAC;QACvE;MACF;MAEA,MAAMoC,KAAK,GACTvF,KAAK,CAACrB,MAAM,KAAK,CAAC,GACd,IAAI,CAACzB,KAAK,CAACsI,QAAQ,CAACxF,KAAK,CAACK,KAAK,EAAE,CAAC,CAAC,GACnC,IAAI,CAACnD,KAAK,CAACsI,QAAQ,CAACxF,KAAK,CAAC;MAChC,IAAI;QAAEK,KAAK;QAAE1B;MAAO,CAAC,GAAGqB,KAAK;MAC7BuF,KAAK,CAAChI,OAAO,CAAC,CAAC4C,IAAI,EAAEsF,CAAC,KAAK;QACzB,IAAIhC,MAAM,EAAE;UACVtD,IAAI,CAACuF,QAAQ,CAAC,CAAC,EAAEJ,GAAG,CAAC;UACrB,IAAIG,CAAC,KAAK,CAAC,EAAE;YACXpF,KAAK,IAAIiF,GAAG,CAAC3G,MAAM;UACrB,CAAC,MAAM;YACLA,MAAM,IAAI2G,GAAG,CAAC3G,MAAM;UACtB;UACA;QACF,CAAC,MAAM,IAAIwB,IAAI,CAACwF,OAAO,CAACC,WAAW,CAACC,UAAU,CAACP,GAAG,CAAC,EAAE;UACnDnF,IAAI,CAAC2F,QAAQ,CAAC,CAAC,EAAER,GAAG,CAAC3G,MAAM,CAAC;UAC5B,IAAI8G,CAAC,KAAK,CAAC,EAAE;YACXpF,KAAK,IAAIiF,GAAG,CAAC3G,MAAM;UACrB,CAAC,MAAM;YACLA,MAAM,IAAI2G,GAAG,CAAC3G,MAAM;UACtB;QACF;MACF,CAAC,CAAC;MACF,IAAI,CAACzB,KAAK,CAAC6I,MAAM,CAAC7J,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;MACrC,IAAI,CAACtF,KAAK,CAACgG,YAAY,CAAC7C,KAAK,EAAE1B,MAAM,EAAEzC,KAAK,CAACqG,OAAO,CAACY,MAAM,CAAC;IAC9D;EACF,CAAC;AACH;AAEA,SAAS8B,qBAAqBA,CAC5BlI,GAAW,EACXW,QAAwB,EACT;EACf,MAAMsI,KAAK,GAAGjJ,GAAG,KAAK,WAAW,GAAG,QAAQ,GAAG,QAAQ;EACvD,OAAO;IACLA,GAAG;IACHW,QAAQ;IACRI,MAAM,EAAE,IAAI;IACZ,CAACkI,KAAK,GAAG,IAAI;IACbnH,OAAOA,CAACmB,KAAK,EAAE;MACb,IAAI;QAAEK;MAAM,CAAC,GAAGL,KAAK;MACrB,IAAIjD,GAAG,KAAK,YAAY,EAAE;QACxBsD,KAAK,IAAIL,KAAK,CAACrB,MAAM,GAAG,CAAC;MAC3B;MACA,MAAM,CAACsH,IAAI,CAAC,GAAG,IAAI,CAAC/I,KAAK,CAACsD,OAAO,CAACH,KAAK,CAAC;MACxC,IAAI,EAAE4F,IAAI,YAAYlK,SAAS,CAAC,EAAE,OAAO,IAAI;MAC7C,IAAIgB,GAAG,KAAK,WAAW,EAAE;QACvB,IAAIW,QAAQ,EAAE;UACZ,IAAI,CAACR,KAAK,CAACgG,YAAY,CACrBlD,KAAK,CAACK,KAAK,GAAG,CAAC,EACfL,KAAK,CAACrB,MAAM,GAAG,CAAC,EAChBzC,KAAK,CAACqG,OAAO,CAACC,IAChB,CAAC;QACH,CAAC,MAAM;UACL,IAAI,CAACtF,KAAK,CAACgG,YAAY,CAAClD,KAAK,CAACK,KAAK,GAAG,CAAC,EAAEnE,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;QAC9D;MACF,CAAC,MAAM,IAAI9E,QAAQ,EAAE;QACnB,IAAI,CAACR,KAAK,CAACgG,YAAY,CACrBlD,KAAK,CAACK,KAAK,EACXL,KAAK,CAACrB,MAAM,GAAG,CAAC,EAChBzC,KAAK,CAACqG,OAAO,CAACC,IAChB,CAAC;MACH,CAAC,MAAM;QACL,IAAI,CAACtF,KAAK,CAACgG,YAAY,CACrBlD,KAAK,CAACK,KAAK,GAAGL,KAAK,CAACrB,MAAM,GAAG,CAAC,EAC9BzC,KAAK,CAACqG,OAAO,CAACC,IAChB,CAAC;MACH;MACA,OAAO,KAAK;IACd;EACF,CAAC;AACH;AAEA,SAASc,iBAAiBA,CAACrC,MAAc,EAAiB;EACxD,OAAO;IACLlE,GAAG,EAAEkE,MAAM,CAAC,CAAC,CAAC;IACdiF,QAAQ,EAAE,IAAI;IACdrH,OAAOA,CAACmB,KAAK,EAAEvB,OAAO,EAAE;MACtB,IAAI,CAACvB,KAAK,CAAC+D,MAAM,CAACA,MAAM,EAAE,CAACxC,OAAO,CAACwC,MAAM,CAACA,MAAM,CAAC,EAAE/E,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;IACxE;EACF,CAAC;AACH;AAEA,SAAS0C,qBAAqBA,CAACiB,EAAW,EAAiB;EACzD,OAAO;IACLpJ,GAAG,EAAEoJ,EAAE,GAAG,SAAS,GAAG,WAAW;IACjCnI,SAAS,EAAE,IAAI;IACfiD,MAAM,EAAE,CAAC,OAAO,CAAC;IACjBpC,OAAOA,CAACmB,KAAK,EAAEvB,OAAO,EAAE;MACtB;MACA,MAAM1B,GAAG,GAAGoJ,EAAE,GAAG,MAAM,GAAG,MAAM;MAChC,MAAM3B,IAAI,GAAG/F,OAAO,CAAC0B,IAAI;MACzB,MAAMiG,SAAS,GAAG5B,IAAI,CAAC6B,MAAM,CAACtJ,GAAG,CAAC;MAClC,IAAIqJ,SAAS,IAAI,IAAI,EAAE;QACrB,IAAIA,SAAS,CAACrE,OAAO,CAACC,QAAQ,KAAK,WAAW,EAAE;UAC9C;UACA,IAAIsE,UAAU,GAAGF,SAAS,CAACG,QAAQ,CAACC,IAAI;UACxC,IAAIxB,GAAG,GAAGR,IAAI;UACd,OAAOQ,GAAG,CAACnD,IAAI,IAAI,IAAI,EAAE;YACvB;YACAmD,GAAG,GAAGA,GAAG,CAACnD,IAAI;YACdyE,UAAU,GAAGA,UAAU,CAAC5D,IAAI;UAC9B;UACA,MAAMrC,KAAK,GACTiG,UAAU,CAAChI,MAAM,CAAC,IAAI,CAACpB,KAAK,CAAC6C,MAAM,CAAC,GACpC0G,IAAI,CAACC,GAAG,CAACjI,OAAO,CAACH,MAAM,EAAEgI,UAAU,CAAC3H,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;UACnD,IAAI,CAACzB,KAAK,CAACgG,YAAY,CAAC7C,KAAK,EAAE,CAAC,EAAEnE,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;QACvD;MACF,CAAC,MAAM;QACL;QACA,MAAMmE,UAAU,GAAGnC,IAAI,CAACT,KAAK,CAAC,CAAC,CAAChH,GAAG,CAAC;QACpC,IAAI4J,UAAU,IAAI,IAAI,EAAE;UACtB,IAAIR,EAAE,EAAE;YACN,IAAI,CAACjJ,KAAK,CAACgG,YAAY,CACrByD,UAAU,CAACrI,MAAM,CAAC,IAAI,CAACpB,KAAK,CAAC6C,MAAM,CAAC,GAAG4G,UAAU,CAAChI,MAAM,CAAC,CAAC,GAAG,CAAC,EAC9D,CAAC,EACDzC,KAAK,CAACqG,OAAO,CAACC,IAChB,CAAC;UACH,CAAC,MAAM;YACL,IAAI,CAACtF,KAAK,CAACgG,YAAY,CACrByD,UAAU,CAACrI,MAAM,CAAC,IAAI,CAACpB,KAAK,CAAC6C,MAAM,CAAC,EACpC,CAAC,EACD7D,KAAK,CAACqG,OAAO,CAACC,IAChB,CAAC;UACH;QACF;MACF;MACA,OAAO,KAAK;IACd;EACF,CAAC;AACH;AAEA,SAAS1D,SAASA,CAACjC,OAAgB,EAAwB;EACzD,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC9DA,OAAO,GAAG;MAAEE,GAAG,EAAEF;IAAQ,CAAC;EAC5B,CAAC,MAAM,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IACtCA,OAAO,GAAGlB,SAAS,CAACkB,OAAO,CAAC;EAC9B,CAAC,MAAM;IACL,OAAO,IAAI;EACb;EACA,IAAIA,OAAO,CAACqJ,QAAQ,EAAE;IACpBrJ,OAAO,CAACP,QAAQ,CAAC,GAAGO,OAAO,CAACqJ,QAAQ;IACpC,OAAOrJ,OAAO,CAACqJ,QAAQ;EACzB;EACA,OAAOrJ,OAAO;AAChB;;AAEA;AACA,SAAS+F,WAAWA,CAAAgE,KAAA,EAAmD;EAAA,IAAlD;IAAE1J,KAAK;IAAE8C;EAAsC,CAAC,GAAA4G,KAAA;EACnE,MAAMrB,KAAK,GAAGrI,KAAK,CAACsI,QAAQ,CAACxF,KAAK,CAAC;EACnC,IAAIyB,OAAO,GAAG,CAAC,CAAC;EAChB,IAAI8D,KAAK,CAAC5G,MAAM,GAAG,CAAC,EAAE;IACpB,MAAMkI,YAAY,GAAGtB,KAAK,CAAC,CAAC,CAAC,CAAC9D,OAAO,CAAC,CAAC;IACvC,MAAMqF,WAAW,GAAGvB,KAAK,CAACA,KAAK,CAAC5G,MAAM,GAAG,CAAC,CAAC,CAAC8C,OAAO,CAAC,CAAC;IACrDA,OAAO,GAAG3F,YAAY,CAACqG,IAAI,CAAC2E,WAAW,EAAED,YAAY,CAAC,IAAI,CAAC,CAAC;EAC9D;EACA3J,KAAK,CAAC2G,UAAU,CAAC7D,KAAK,EAAE9D,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;EAC3C,IAAInF,MAAM,CAACC,IAAI,CAACmE,OAAO,CAAC,CAAC9C,MAAM,GAAG,CAAC,EAAE;IACnCzB,KAAK,CAACgH,UAAU,CAAClE,KAAK,CAACK,KAAK,EAAE,CAAC,EAAEoB,OAAO,EAAEvF,KAAK,CAACqG,OAAO,CAACC,IAAI,CAAC;EAC/D;EACAtF,KAAK,CAACgG,YAAY,CAAClD,KAAK,CAACK,KAAK,EAAEnE,KAAK,CAACqG,OAAO,CAACY,MAAM,CAAC;AACvD;AAEA,SAASwB,SAASA,CAACoC,MAAe,EAAExC,GAAS,EAAEC,IAAU,EAAElG,MAAc,EAAE;EACzE,IAAIiG,GAAG,CAAC1C,IAAI,IAAI,IAAI,IAAI0C,GAAG,CAAC7B,IAAI,IAAI,IAAI,EAAE;IACxC,IAAI8B,IAAI,CAAC3C,IAAI,IAAI,IAAI,IAAI2C,IAAI,CAAC9B,IAAI,IAAI,IAAI,EAAE;MAC1C,OAAOpE,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC9B;IACA,OAAOkG,IAAI,CAAC3C,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;EACnC;EACA,IAAI0C,GAAG,CAAC1C,IAAI,IAAI,IAAI,EAAE;IACpB,OAAO,CAAC,CAAC;EACX;EACA,IAAI0C,GAAG,CAAC7B,IAAI,IAAI,IAAI,EAAE;IACpB,OAAO,CAAC;EACV;EACA,OAAO,IAAI;AACb;AAEA,SAAShG,QAAQ,IAAIsK,OAAO,EAAE1K,QAAQ,EAAEwC,SAAS,EAAE8D,WAAW", "ignoreList": []}