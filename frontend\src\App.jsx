import { useState, useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import './App.css'

// Auth Components
import Register from './components/auth/Register'
import VerifyOTP from './components/auth/VerifyOTP'
import Login from './components/auth/Login'
import AdminLogin from './components/auth/AdminLogin'
import SignAgreement from './components/auth/SignAgreement'

// User Components
import Dashboard from './components/dashboard/Dashboard'
import WorkEditor from './components/work/WorkEditor'
import UserProfile from './components/user/UserProfile'

// Admin Components
import AdminDashboard from './components/admin/AdminDashboard'
import UserManagement from './components/admin/UserManagement'
import AgreementManagement from './components/admin/AgreementManagement'

// Context
import { AuthProvider } from './context/AuthContext'

// Protected Routes
const ProtectedRoute = ({ children, allowedRoles }) => {
  const token = localStorage.getItem('token')
  const userRole = localStorage.getItem('userRole')
  
  if (!token) {
    return <Navigate to="/login" />
  }
  
  if (allowedRoles && !allowedRoles.includes(userRole)) {
    return <Navigate to="/" />
  }
  
  return children
}

function App() {

  return (
    <>
        <h1 class="text-3xl font-bold underline">
    Hello world!
  </h1>
    </>
  )
}

export default App

