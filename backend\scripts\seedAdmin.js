const mongoose = require('mongoose');
const User = require('../models/User');
const Agreement = require('../models/Agreement');
require('dotenv').config();

const seedAdmin = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log('Connected to MongoDB');

    // Check if admin already exists
    const existingAdmin = await User.findOne({ email: '<EMAIL>' });
    if (existingAdmin) {
      console.log('Admin user already exists');
      return;
    }

    // Create admin user
    const admin = new User({
      name: 'Admin User',
      email: '<EMAIL>',
      mobileNumber: '9999999999',
      password: 'admin123',
      role: 'admin',
      isVerified: true,
      isSignedAgreement: true
    });

    await admin.save();
    console.log('Admin user created successfully');
    console.log('Email: <EMAIL>');
    console.log('Password: admin123');

    // Create default agreement if none exists
    const existingAgreement = await Agreement.findOne();
    if (!existingAgreement) {
      const defaultAgreement = new Agreement({
        content: `TERMS AND CONDITIONS AGREEMENT

By signing this agreement, you acknowledge and agree to the following terms and conditions:

1. WORK ASSIGNMENT
   - You will be assigned a work task that must be completed within 4 days of starting.
   - The work must be original and completed by you personally.
   - You may save drafts and continue working until submission.

2. DEADLINE COMPLIANCE
   - You have exactly 4 days (96 hours) from the time you start work to submit.
   - Failure to submit within the deadline will result in automatic penalties.
   - Daily reminders will be sent as the deadline approaches.

3. SUBMISSION REQUIREMENTS
   - All work must be submitted through the provided online editor.
   - Once submitted, no further changes can be made.
   - Submissions are final and binding.

4. PENALTIES
   - Late submission will result in automatic penalty status.
   - Penalized users may face restrictions on future assignments.

5. COMMUNICATION
   - All official communication will be sent to your registered email address.
   - You are responsible for checking your email regularly.
   - OTP verification is required for account security.

6. AGREEMENT MODIFICATIONS
   - These terms may be updated from time to time.
   - You will be notified of any significant changes.
   - Continued use of the platform constitutes acceptance of updated terms.

By providing your digital signature below, you confirm that you have read, understood, and agree to be bound by these terms and conditions.

Date: [Current Date]
Signature: [Digital Signature Required]`,
        version: 1
      });

      await defaultAgreement.save();
      console.log('Default agreement created successfully');
    }

    process.exit(0);
  } catch (error) {
    console.error('Error seeding admin:', error);
    process.exit(1);
  }
};

seedAdmin();
