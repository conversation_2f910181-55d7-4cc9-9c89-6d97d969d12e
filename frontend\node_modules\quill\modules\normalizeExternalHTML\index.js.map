{"version": 3, "file": "index.js", "names": ["googleDocs", "msWord", "NORMALIZERS", "normalizeExternalHTML", "doc", "documentElement", "for<PERSON>ach", "normalize"], "sources": ["../../../src/modules/normalizeExternalHTML/index.ts"], "sourcesContent": ["import googleDocs from './normalizers/googleDocs.js';\nimport msWord from './normalizers/msWord.js';\n\nconst NORMALIZERS = [msWord, googleDocs];\n\nconst normalizeExternalHTML = (doc: Document) => {\n  if (doc.documentElement) {\n    NORMALIZERS.forEach((normalize) => {\n      normalize(doc);\n    });\n  }\n};\n\nexport default normalizeExternalHTML;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,6BAA6B;AACpD,OAAOC,MAAM,MAAM,yBAAyB;AAE5C,MAAMC,WAAW,GAAG,CAACD,MAAM,EAAED,UAAU,CAAC;AAExC,MAAMG,qBAAqB,GAAIC,GAAa,IAAK;EAC/C,IAAIA,GAAG,CAACC,eAAe,EAAE;IACvBH,WAAW,CAACI,OAAO,CAAEC,SAAS,IAAK;MACjCA,SAAS,CAACH,GAAG,CAAC;IAChB,CAAC,CAAC;EACJ;AACF,CAAC;AAED,eAAeD,qBAAqB", "ignoreList": []}