{"version": 3, "file": "direction.js", "names": ["Attributor", "ClassAttributor", "<PERSON><PERSON>", "StyleAttributor", "config", "scope", "BLOCK", "whitelist", "DirectionAttribute", "DirectionClass", "DirectionStyle"], "sources": ["../../src/formats/direction.ts"], "sourcesContent": ["import { Attributor, ClassAttributor, Scope, StyleAttributor } from 'parchment';\n\nconst config = {\n  scope: Scope.BLOCK,\n  whitelist: ['rtl'],\n};\n\nconst DirectionAttribute = new Attributor('direction', 'dir', config);\nconst DirectionClass = new ClassAttributor('direction', 'ql-direction', config);\nconst DirectionStyle = new StyleAttributor('direction', 'direction', config);\n\nexport { DirectionAttribute, DirectionClass, DirectionStyle };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,eAAe,EAAEC,KAAK,EAAEC,eAAe,QAAQ,WAAW;AAE/E,MAAMC,MAAM,GAAG;EACbC,KAAK,EAAEH,KAAK,CAACI,KAAK;EAClBC,SAAS,EAAE,CAAC,KAAK;AACnB,CAAC;AAED,MAAMC,kBAAkB,GAAG,IAAIR,UAAU,CAAC,WAAW,EAAE,KAAK,EAAEI,MAAM,CAAC;AACrE,MAAMK,cAAc,GAAG,IAAIR,eAAe,CAAC,WAAW,EAAE,cAAc,EAAEG,MAAM,CAAC;AAC/E,MAAMM,cAAc,GAAG,IAAIP,eAAe,CAAC,WAAW,EAAE,WAAW,EAAEC,MAAM,CAAC;AAE5E,SAASI,kBAAkB,EAAEC,cAAc,EAAEC,cAAc", "ignoreList": []}