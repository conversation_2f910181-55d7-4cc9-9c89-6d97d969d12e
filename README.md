# MERN Stack Application with JWT Authentication

A full-stack web application built with MongoDB, Express.js, React, and Node.js featuring user registration, email verification, digital agreement signing, work management with deadlines, and admin dashboard.

## Features

### 🔐 Authentication System
- User registration with email verification (6-digit OTP)
- Digital agreement signing with signature canvas
- JWT-based authentication
- Separate admin login
- Password stored as plain text (as per requirements)

### 👤 User Features
- Email verification via OTP
- Digital signature for agreement
- Work dashboard with 4-day deadline timer
- MS Word-like editor (React Quill)
- Auto-save drafts every 30 seconds
- Work submission with deadline tracking
- Email alerts for deadline reminders
- Profile management

### 🛠 Admin Features
- Complete admin dashboard
- User management (CRUD operations)
- Agreement content management
- Manual OTP and agreement link sending
- User status monitoring (verified, signed, working, submitted, penalized)
- System statistics and analytics

### ⚡ Work Management
- 4-day deadline from work start
- Auto-save functionality
- Draft resume capability
- Deadline alerts (3, 2, 1 days remaining)
- Automatic penalty for late submission
- Read-only mode after submission/deadline

## Tech Stack

### Frontend
- **React 19** with Vite
- **Tailwind CSS** for styling
- **React Router** for navigation
- **React Quill** for rich text editing
- **React Signature Canvas** for digital signatures
- **Axios** for API calls

### Backend
- **Node.js** with Express.js
- **MongoDB** with Mongoose
- **JWT** for authentication
- **Nodemailer** for email services
- **CORS** for cross-origin requests

## Installation & Setup

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local or cloud)
- Gmail account for email services

### 1. Clone the Repository
```bash
git clone <repository-url>
cd mern-scam-app
```

### 2. Backend Setup
```bash
cd backend
npm install
```

Create `.env` file in backend directory:
```env
PORT=5000
MONGO_URI=mongodb://localhost:27017/mern_scam_app
JWT_SECRET=your_jwt_secret_key_here_make_it_long_and_secure
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password_here
FRONTEND_URL=http://localhost:5173
```

**Email Setup:**
1. Enable 2-factor authentication on your Gmail account
2. Generate an App Password for Gmail
3. Use the App Password in EMAIL_PASS (not your regular password)

### 3. Frontend Setup
```bash
cd frontend
npm install
```

Create `.env` file in frontend directory:
```env
VITE_API_URL=http://localhost:5000
```

### 4. Database Setup
```bash
cd backend
npm run seed
```
This creates an admin user:
- **Email:** <EMAIL>
- **Password:** admin123

### 5. Start the Application

**Backend (Terminal 1):**
```bash
cd backend
npm start
# or for development with auto-reload:
npm run dev
```

**Frontend (Terminal 2):**
```bash
cd frontend
npm run dev
```

The application will be available at:
- **Frontend:** http://localhost:5173
- **Backend API:** http://localhost:5000

## Usage Guide

### For Users
1. **Register:** Create account with name, email, mobile numbers, and password
2. **Verify Email:** Enter 6-digit OTP sent to your email
3. **Sign Agreement:** Use digital signature pad to sign the agreement
4. **Login:** Access your dashboard after verification and agreement signing
5. **Start Work:** Begin your 4-day work assignment
6. **Work on Assignment:** Use the rich text editor with auto-save
7. **Submit Work:** Submit before the 4-day deadline

### For Admins
1. **Login:** Use admin credentials at `/admin-login`
2. **Dashboard:** View system statistics and user status
3. **Manage Users:** Create, edit, delete users
4. **Send Communications:** Manually send OTP or agreement links
5. **Manage Agreements:** Create and edit agreement content
6. **Monitor Progress:** Track user work status and deadlines

## API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/verify-otp` - Email verification
- `POST /api/auth/login` - User login
- `POST /api/auth/admin-login` - Admin login
- `POST /api/auth/sign-agreement` - Sign agreement
- `GET /api/auth/agreement` - Get current agreement

### User Management
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `GET /api/users/dashboard` - Get dashboard data

### Work Management
- `POST /api/work/start` - Start work assignment
- `POST /api/work/save-draft` - Save work draft
- `GET /api/work/draft` - Get saved draft
- `POST /api/work/submit` - Submit completed work

### Admin Operations
- `GET /api/admin/dashboard-stats` - Get system statistics
- `GET /api/admin/users` - Get all users
- `POST /api/admin/users` - Create new user
- `PUT /api/admin/users/:id` - Update user
- `DELETE /api/admin/users/:id` - Delete user
- `POST /api/admin/users/:id/send-otp` - Send OTP manually
- `POST /api/admin/users/:id/send-agreement` - Send agreement link
- `GET /api/admin/agreements` - Get all agreements
- `POST /api/admin/agreements` - Create new agreement
- `PUT /api/admin/agreements/:id` - Update agreement
- `DELETE /api/admin/agreements/:id` - Delete agreement

## Key Features Implementation

### Email System
- OTP generation and verification
- Agreement link distribution
- Deadline reminder alerts
- Uses Gmail SMTP with App Passwords

### Work Timer System
- 4-day countdown from work start
- Real-time deadline tracking
- Automatic penalty assignment
- Email notifications at 3, 2, 1 days remaining

### Security Features
- JWT token authentication
- Role-based access control
- Protected routes for users and admins
- Input validation and sanitization

### User Experience
- Responsive design with Tailwind CSS
- Auto-save functionality
- Real-time status updates
- Intuitive admin dashboard
- Digital signature integration

## Troubleshooting

### Common Issues

1. **Email not sending:**
   - Verify Gmail App Password is correct
   - Check 2FA is enabled on Gmail account
   - Ensure EMAIL_USER and EMAIL_PASS are set correctly

2. **Database connection error:**
   - Verify MongoDB is running
   - Check MONGO_URI in .env file
   - Ensure database permissions are correct

3. **Frontend not connecting to backend:**
   - Verify VITE_API_URL in frontend .env
   - Check backend is running on correct port
   - Ensure CORS is properly configured

4. **JWT token issues:**
   - Verify JWT_SECRET is set in backend .env
   - Check token expiration settings
   - Clear localStorage and login again

## Development Notes

- Passwords are stored as plain text (as per requirements)
- Auto-save occurs every 30 seconds during work
- Deadline checking should be implemented as a cron job in production
- Email templates can be customized in the controllers
- Agreement content supports rich text formatting

## Production Deployment

For production deployment:
1. Set up MongoDB Atlas or production database
2. Configure production email service
3. Set secure JWT secrets
4. Enable HTTPS
5. Set up proper environment variables
6. Implement cron jobs for deadline checking
7. Configure proper CORS origins

## License

This project is for educational purposes. Please ensure compliance with data protection regulations when handling user information.
