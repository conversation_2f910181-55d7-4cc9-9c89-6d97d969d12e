{"version": 3, "file": "scroll.js", "names": ["ContainerBlot", "LeafBlot", "<PERSON><PERSON>", "ScrollBlot", "Delta", "AttributeMap", "Op", "Emitter", "Block", "BlockEmbed", "bubbleFormats", "Break", "Container", "isLine", "blot", "isUpdatable", "updateContent", "<PERSON><PERSON>", "blotName", "className", "tagName", "defaultChild", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "registry", "domNode", "_ref", "emitter", "batch", "optimize", "enable", "addEventListener", "e", "handleDragStart", "batchStart", "Array", "isArray", "batchEnd", "mutations", "update", "emitMount", "emit", "events", "SCROLL_BLOT_MOUNT", "emitUnmount", "SCROLL_BLOT_UNMOUNT", "emitEmbedUpdate", "change", "SCROLL_EMBED_UPDATE", "deleteAt", "index", "length", "first", "offset", "line", "last", "ref", "children", "head", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "remove", "enabled", "arguments", "undefined", "setAttribute", "formatAt", "format", "value", "insertAt", "def", "scroll", "query", "BLOCK", "create", "statics", "append<PERSON><PERSON><PERSON>", "endsWith", "slice", "embed", "insertBefore", "scope", "INLINE_BLOT", "wrapper", "insertContents", "delta", "renderBlocks", "deltaToRenderBlocks", "concat", "insert", "pop", "shift", "shouldInsertNewlineChar", "type", "descendant", "key", "insertInlineContents", "newlineCharLength", "lineEndIndex", "formats", "attributes", "diff", "Object", "keys", "for<PERSON>ach", "name", "refBlot", "refBlotOffset", "find", "split", "renderBlock", "block", "createBlock", "blockEmbed", "isEnabled", "getAttribute", "leaf", "path", "lines", "Number", "MAX_VALUE", "getLines", "blotIndex", "blotLength", "lengthLeft", "forEachAt", "child", "childIndex", "<PERSON><PERSON><PERSON><PERSON>", "push", "context", "SCROLL_OPTIMIZE", "source", "sources", "USER", "observer", "takeRecords", "filter", "_ref2", "target", "SCROLL_BEFORE_UPDATE", "SCROLL_UPDATE", "updateEmbedAt", "b", "event", "preventDefault", "currentBlockDelta", "op", "splitted", "text", "INLINE", "entries", "_ref3", "isBlockBlot", "BLOT", "_ref4", "parent", "inlineContents", "reduce", "isInlineEmbed"], "sources": ["../../src/blots/scroll.ts"], "sourcesContent": ["import { Container<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, ScrollBlot } from 'parchment';\nimport type { Blot, Parent, EmbedBlot, ParentBlot, Registry } from 'parchment';\nimport Delta, { AttributeMap, Op } from 'quill-delta';\nimport Emitter from '../core/emitter.js';\nimport type { EmitterSource } from '../core/emitter.js';\nimport Block, { BlockEmbed, bubbleFormats } from './block.js';\nimport Break from './break.js';\nimport Container from './container.js';\n\ntype RenderBlock =\n  | {\n      type: 'blockEmbed';\n      attributes: AttributeMap;\n      key: string;\n      value: unknown;\n    }\n  | { type: 'block'; attributes: AttributeMap; delta: Delta };\n\nfunction isLine(blot: unknown): blot is Block | BlockEmbed {\n  return blot instanceof Block || blot instanceof BlockEmbed;\n}\n\ninterface UpdatableEmbed {\n  updateContent(change: unknown): void;\n}\n\nfunction isUpdatable(blot: Blot): blot is Blot & UpdatableEmbed {\n  return typeof (blot as unknown as any).updateContent === 'function';\n}\n\nclass Scroll extends ScrollBlot {\n  static blotName = 'scroll';\n  static className = 'ql-editor';\n  static tagName = 'DIV';\n  static defaultChild = Block;\n  static allowedChildren = [Block, BlockEmbed, Container];\n\n  emitter: Emitter;\n  batch: false | MutationRecord[];\n\n  constructor(\n    registry: Registry,\n    domNode: HTMLDivElement,\n    { emitter }: { emitter: Emitter },\n  ) {\n    super(registry, domNode);\n    this.emitter = emitter;\n    this.batch = false;\n    this.optimize();\n    this.enable();\n    this.domNode.addEventListener('dragstart', (e) => this.handleDragStart(e));\n  }\n\n  batchStart() {\n    if (!Array.isArray(this.batch)) {\n      this.batch = [];\n    }\n  }\n\n  batchEnd() {\n    if (!this.batch) return;\n    const mutations = this.batch;\n    this.batch = false;\n    this.update(mutations);\n  }\n\n  emitMount(blot: Blot) {\n    this.emitter.emit(Emitter.events.SCROLL_BLOT_MOUNT, blot);\n  }\n\n  emitUnmount(blot: Blot) {\n    this.emitter.emit(Emitter.events.SCROLL_BLOT_UNMOUNT, blot);\n  }\n\n  emitEmbedUpdate(blot: Blot, change: unknown) {\n    this.emitter.emit(Emitter.events.SCROLL_EMBED_UPDATE, blot, change);\n  }\n\n  deleteAt(index: number, length: number) {\n    const [first, offset] = this.line(index);\n    const [last] = this.line(index + length);\n    super.deleteAt(index, length);\n    if (last != null && first !== last && offset > 0) {\n      if (first instanceof BlockEmbed || last instanceof BlockEmbed) {\n        this.optimize();\n        return;\n      }\n      const ref =\n        last.children.head instanceof Break ? null : last.children.head;\n      // @ts-expect-error\n      first.moveChildren(last, ref);\n      // @ts-expect-error\n      first.remove();\n    }\n    this.optimize();\n  }\n\n  enable(enabled = true) {\n    this.domNode.setAttribute('contenteditable', enabled ? 'true' : 'false');\n  }\n\n  formatAt(index: number, length: number, format: string, value: unknown) {\n    super.formatAt(index, length, format, value);\n    this.optimize();\n  }\n\n  insertAt(index: number, value: string, def?: unknown) {\n    if (index >= this.length()) {\n      if (def == null || this.scroll.query(value, Scope.BLOCK) == null) {\n        const blot = this.scroll.create(this.statics.defaultChild.blotName);\n        this.appendChild(blot);\n        if (def == null && value.endsWith('\\n')) {\n          blot.insertAt(0, value.slice(0, -1), def);\n        } else {\n          blot.insertAt(0, value, def);\n        }\n      } else {\n        const embed = this.scroll.create(value, def);\n        this.appendChild(embed);\n      }\n    } else {\n      super.insertAt(index, value, def);\n    }\n    this.optimize();\n  }\n\n  insertBefore(blot: Blot, ref?: Blot | null) {\n    if (blot.statics.scope === Scope.INLINE_BLOT) {\n      const wrapper = this.scroll.create(\n        this.statics.defaultChild.blotName,\n      ) as Parent;\n      wrapper.appendChild(blot);\n      super.insertBefore(wrapper, ref);\n    } else {\n      super.insertBefore(blot, ref);\n    }\n  }\n\n  insertContents(index: number, delta: Delta) {\n    const renderBlocks = this.deltaToRenderBlocks(\n      delta.concat(new Delta().insert('\\n')),\n    );\n    const last = renderBlocks.pop();\n    if (last == null) return;\n\n    this.batchStart();\n\n    const first = renderBlocks.shift();\n    if (first) {\n      const shouldInsertNewlineChar =\n        first.type === 'block' &&\n        (first.delta.length() === 0 ||\n          (!this.descendant(BlockEmbed, index)[0] && index < this.length()));\n      const delta =\n        first.type === 'block'\n          ? first.delta\n          : new Delta().insert({ [first.key]: first.value });\n      insertInlineContents(this, index, delta);\n      const newlineCharLength = first.type === 'block' ? 1 : 0;\n      const lineEndIndex = index + delta.length() + newlineCharLength;\n      if (shouldInsertNewlineChar) {\n        this.insertAt(lineEndIndex - 1, '\\n');\n      }\n\n      const formats = bubbleFormats(this.line(index)[0]);\n      const attributes = AttributeMap.diff(formats, first.attributes) || {};\n      Object.keys(attributes).forEach((name) => {\n        this.formatAt(lineEndIndex - 1, 1, name, attributes[name]);\n      });\n\n      index = lineEndIndex;\n    }\n\n    let [refBlot, refBlotOffset] = this.children.find(index);\n    if (renderBlocks.length) {\n      if (refBlot) {\n        refBlot = refBlot.split(refBlotOffset);\n        refBlotOffset = 0;\n      }\n\n      renderBlocks.forEach((renderBlock) => {\n        if (renderBlock.type === 'block') {\n          const block = this.createBlock(\n            renderBlock.attributes,\n            refBlot || undefined,\n          );\n          insertInlineContents(block, 0, renderBlock.delta);\n        } else {\n          const blockEmbed = this.create(\n            renderBlock.key,\n            renderBlock.value,\n          ) as EmbedBlot;\n          this.insertBefore(blockEmbed, refBlot || undefined);\n          Object.keys(renderBlock.attributes).forEach((name) => {\n            blockEmbed.format(name, renderBlock.attributes[name]);\n          });\n        }\n      });\n    }\n\n    if (last.type === 'block' && last.delta.length()) {\n      const offset = refBlot\n        ? refBlot.offset(refBlot.scroll) + refBlotOffset\n        : this.length();\n      insertInlineContents(this, offset, last.delta);\n    }\n\n    this.batchEnd();\n    this.optimize();\n  }\n\n  isEnabled() {\n    return this.domNode.getAttribute('contenteditable') === 'true';\n  }\n\n  leaf(index: number): [LeafBlot | null, number] {\n    const last = this.path(index).pop();\n    if (!last) {\n      return [null, -1];\n    }\n\n    const [blot, offset] = last;\n    return blot instanceof LeafBlot ? [blot, offset] : [null, -1];\n  }\n\n  line(index: number): [Block | BlockEmbed | null, number] {\n    if (index === this.length()) {\n      return this.line(index - 1);\n    }\n    // @ts-expect-error TODO: make descendant() generic\n    return this.descendant(isLine, index);\n  }\n\n  lines(index = 0, length = Number.MAX_VALUE): (Block | BlockEmbed)[] {\n    const getLines = (\n      blot: ParentBlot,\n      blotIndex: number,\n      blotLength: number,\n    ) => {\n      let lines: (Block | BlockEmbed)[] = [];\n      let lengthLeft = blotLength;\n      blot.children.forEachAt(\n        blotIndex,\n        blotLength,\n        (child, childIndex, childLength) => {\n          if (isLine(child)) {\n            lines.push(child);\n          } else if (child instanceof ContainerBlot) {\n            lines = lines.concat(getLines(child, childIndex, lengthLeft));\n          }\n          lengthLeft -= childLength;\n        },\n      );\n      return lines;\n    };\n    return getLines(this, index, length);\n  }\n\n  optimize(context?: { [key: string]: any }): void;\n  optimize(\n    mutations?: MutationRecord[],\n    context?: { [key: string]: any },\n  ): void;\n  optimize(mutations = [], context = {}) {\n    if (this.batch) return;\n    super.optimize(mutations, context);\n    if (mutations.length > 0) {\n      this.emitter.emit(Emitter.events.SCROLL_OPTIMIZE, mutations, context);\n    }\n  }\n\n  path(index: number) {\n    return super.path(index).slice(1); // Exclude self\n  }\n\n  remove() {\n    // Never remove self\n  }\n\n  update(source?: EmitterSource): void;\n  update(mutations?: MutationRecord[]): void;\n  update(mutations?: MutationRecord[] | EmitterSource): void {\n    if (this.batch) {\n      if (Array.isArray(mutations)) {\n        this.batch = this.batch.concat(mutations);\n      }\n      return;\n    }\n    let source: EmitterSource = Emitter.sources.USER;\n    if (typeof mutations === 'string') {\n      source = mutations;\n    }\n    if (!Array.isArray(mutations)) {\n      mutations = this.observer.takeRecords();\n    }\n    mutations = mutations.filter(({ target }) => {\n      const blot = this.find(target, true);\n      return blot && !isUpdatable(blot);\n    });\n    if (mutations.length > 0) {\n      this.emitter.emit(Emitter.events.SCROLL_BEFORE_UPDATE, source, mutations);\n    }\n    super.update(mutations.concat([])); // pass copy\n    if (mutations.length > 0) {\n      this.emitter.emit(Emitter.events.SCROLL_UPDATE, source, mutations);\n    }\n  }\n\n  updateEmbedAt(index: number, key: string, change: unknown) {\n    // Currently it only supports top-level embeds (BlockEmbed).\n    // We can update `ParentBlot` in parchment to support inline embeds.\n    const [blot] = this.descendant((b: Blot) => b instanceof BlockEmbed, index);\n    if (blot && blot.statics.blotName === key && isUpdatable(blot)) {\n      blot.updateContent(change);\n    }\n  }\n\n  protected handleDragStart(event: DragEvent) {\n    event.preventDefault();\n  }\n\n  private deltaToRenderBlocks(delta: Delta) {\n    const renderBlocks: RenderBlock[] = [];\n\n    let currentBlockDelta = new Delta();\n    delta.forEach((op) => {\n      const insert = op?.insert;\n      if (!insert) return;\n      if (typeof insert === 'string') {\n        const splitted = insert.split('\\n');\n        splitted.slice(0, -1).forEach((text) => {\n          currentBlockDelta.insert(text, op.attributes);\n          renderBlocks.push({\n            type: 'block',\n            delta: currentBlockDelta,\n            attributes: op.attributes ?? {},\n          });\n          currentBlockDelta = new Delta();\n        });\n        const last = splitted[splitted.length - 1];\n        if (last) {\n          currentBlockDelta.insert(last, op.attributes);\n        }\n      } else {\n        const key = Object.keys(insert)[0];\n        if (!key) return;\n        if (this.query(key, Scope.INLINE)) {\n          currentBlockDelta.push(op);\n        } else {\n          if (currentBlockDelta.length()) {\n            renderBlocks.push({\n              type: 'block',\n              delta: currentBlockDelta,\n              attributes: {},\n            });\n          }\n          currentBlockDelta = new Delta();\n          renderBlocks.push({\n            type: 'blockEmbed',\n            key,\n            value: insert[key],\n            attributes: op.attributes ?? {},\n          });\n        }\n      }\n    });\n\n    if (currentBlockDelta.length()) {\n      renderBlocks.push({\n        type: 'block',\n        delta: currentBlockDelta,\n        attributes: {},\n      });\n    }\n\n    return renderBlocks;\n  }\n\n  private createBlock(attributes: AttributeMap, refBlot?: Blot) {\n    let blotName: string | undefined;\n    const formats: AttributeMap = {};\n\n    Object.entries(attributes).forEach(([key, value]) => {\n      const isBlockBlot = this.query(key, Scope.BLOCK & Scope.BLOT) != null;\n      if (isBlockBlot) {\n        blotName = key;\n      } else {\n        formats[key] = value;\n      }\n    });\n\n    const block = this.create(\n      blotName || this.statics.defaultChild.blotName,\n      blotName ? attributes[blotName] : undefined,\n    ) as ParentBlot;\n\n    this.insertBefore(block, refBlot || undefined);\n\n    const length = block.length();\n    Object.entries(formats).forEach(([key, value]) => {\n      block.formatAt(0, length, key, value);\n    });\n\n    return block;\n  }\n}\n\nfunction insertInlineContents(\n  parent: ParentBlot,\n  index: number,\n  inlineContents: Delta,\n) {\n  inlineContents.reduce((index, op) => {\n    const length = Op.length(op);\n    let attributes = op.attributes || {};\n    if (op.insert != null) {\n      if (typeof op.insert === 'string') {\n        const text = op.insert;\n        parent.insertAt(index, text);\n        const [leaf] = parent.descendant(LeafBlot, index);\n        const formats = bubbleFormats(leaf);\n        attributes = AttributeMap.diff(formats, attributes) || {};\n      } else if (typeof op.insert === 'object') {\n        const key = Object.keys(op.insert)[0]; // There should only be one key\n        if (key == null) return index;\n        parent.insertAt(index, key, op.insert[key]);\n        const isInlineEmbed = parent.scroll.query(key, Scope.INLINE) != null;\n        if (isInlineEmbed) {\n          const [leaf] = parent.descendant(LeafBlot, index);\n          const formats = bubbleFormats(leaf);\n          attributes = AttributeMap.diff(formats, attributes) || {};\n        }\n      }\n    }\n    Object.keys(attributes).forEach((key) => {\n      parent.formatAt(index, length, key, attributes[key]);\n    });\n    return index + length;\n  }, index);\n}\n\nexport default Scroll;\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,UAAU,QAAQ,WAAW;AAEtE,OAAOC,KAAK,IAAIC,YAAY,EAAEC,EAAE,QAAQ,aAAa;AACrD,OAAOC,OAAO,MAAM,oBAAoB;AAExC,OAAOC,KAAK,IAAIC,UAAU,EAAEC,aAAa,QAAQ,YAAY;AAC7D,OAAOC,KAAK,MAAM,YAAY;AAC9B,OAAOC,SAAS,MAAM,gBAAgB;AAWtC,SAASC,MAAMA,CAACC,IAAa,EAA8B;EACzD,OAAOA,IAAI,YAAYN,KAAK,IAAIM,IAAI,YAAYL,UAAU;AAC5D;AAMA,SAASM,WAAWA,CAACD,IAAU,EAAiC;EAC9D,OAAO,OAAQA,IAAI,CAAoBE,aAAa,KAAK,UAAU;AACrE;AAEA,MAAMC,MAAM,SAASd,UAAU,CAAC;EAC9B,OAAOe,QAAQ,GAAG,QAAQ;EAC1B,OAAOC,SAAS,GAAG,WAAW;EAC9B,OAAOC,OAAO,GAAG,KAAK;EACtB,OAAOC,YAAY,GAAGb,KAAK;EAC3B,OAAOc,eAAe,GAAG,CAACd,KAAK,EAAEC,UAAU,EAAEG,SAAS,CAAC;EAKvDW,WAAWA,CACTC,QAAkB,EAClBC,OAAuB,EAAAC,IAAA,EAEvB;IAAA,IADA;MAAEC;IAA8B,CAAC,GAAAD,IAAA;IAEjC,KAAK,CAACF,QAAQ,EAAEC,OAAO,CAAC;IACxB,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACC,QAAQ,CAAC,CAAC;IACf,IAAI,CAACC,MAAM,CAAC,CAAC;IACb,IAAI,CAACL,OAAO,CAACM,gBAAgB,CAAC,WAAW,EAAGC,CAAC,IAAK,IAAI,CAACC,eAAe,CAACD,CAAC,CAAC,CAAC;EAC5E;EAEAE,UAAUA,CAAA,EAAG;IACX,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC,IAAI,CAACR,KAAK,CAAC,EAAE;MAC9B,IAAI,CAACA,KAAK,GAAG,EAAE;IACjB;EACF;EAEAS,QAAQA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACT,KAAK,EAAE;IACjB,MAAMU,SAAS,GAAG,IAAI,CAACV,KAAK;IAC5B,IAAI,CAACA,KAAK,GAAG,KAAK;IAClB,IAAI,CAACW,MAAM,CAACD,SAAS,CAAC;EACxB;EAEAE,SAASA,CAAC1B,IAAU,EAAE;IACpB,IAAI,CAACa,OAAO,CAACc,IAAI,CAAClC,OAAO,CAACmC,MAAM,CAACC,iBAAiB,EAAE7B,IAAI,CAAC;EAC3D;EAEA8B,WAAWA,CAAC9B,IAAU,EAAE;IACtB,IAAI,CAACa,OAAO,CAACc,IAAI,CAAClC,OAAO,CAACmC,MAAM,CAACG,mBAAmB,EAAE/B,IAAI,CAAC;EAC7D;EAEAgC,eAAeA,CAAChC,IAAU,EAAEiC,MAAe,EAAE;IAC3C,IAAI,CAACpB,OAAO,CAACc,IAAI,CAAClC,OAAO,CAACmC,MAAM,CAACM,mBAAmB,EAAElC,IAAI,EAAEiC,MAAM,CAAC;EACrE;EAEAE,QAAQA,CAACC,KAAa,EAAEC,MAAc,EAAE;IACtC,MAAM,CAACC,KAAK,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,IAAI,CAACJ,KAAK,CAAC;IACxC,MAAM,CAACK,IAAI,CAAC,GAAG,IAAI,CAACD,IAAI,CAACJ,KAAK,GAAGC,MAAM,CAAC;IACxC,KAAK,CAACF,QAAQ,CAACC,KAAK,EAAEC,MAAM,CAAC;IAC7B,IAAII,IAAI,IAAI,IAAI,IAAIH,KAAK,KAAKG,IAAI,IAAIF,MAAM,GAAG,CAAC,EAAE;MAChD,IAAID,KAAK,YAAY3C,UAAU,IAAI8C,IAAI,YAAY9C,UAAU,EAAE;QAC7D,IAAI,CAACoB,QAAQ,CAAC,CAAC;QACf;MACF;MACA,MAAM2B,GAAG,GACPD,IAAI,CAACE,QAAQ,CAACC,IAAI,YAAY/C,KAAK,GAAG,IAAI,GAAG4C,IAAI,CAACE,QAAQ,CAACC,IAAI;MACjE;MACAN,KAAK,CAACO,YAAY,CAACJ,IAAI,EAAEC,GAAG,CAAC;MAC7B;MACAJ,KAAK,CAACQ,MAAM,CAAC,CAAC;IAChB;IACA,IAAI,CAAC/B,QAAQ,CAAC,CAAC;EACjB;EAEAC,MAAMA,CAAA,EAAiB;IAAA,IAAhB+B,OAAO,GAAAC,SAAA,CAAAX,MAAA,QAAAW,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI;IACnB,IAAI,CAACrC,OAAO,CAACuC,YAAY,CAAC,iBAAiB,EAAEH,OAAO,GAAG,MAAM,GAAG,OAAO,CAAC;EAC1E;EAEAI,QAAQA,CAACf,KAAa,EAAEC,MAAc,EAAEe,MAAc,EAAEC,KAAc,EAAE;IACtE,KAAK,CAACF,QAAQ,CAACf,KAAK,EAAEC,MAAM,EAAEe,MAAM,EAAEC,KAAK,CAAC;IAC5C,IAAI,CAACtC,QAAQ,CAAC,CAAC;EACjB;EAEAuC,QAAQA,CAAClB,KAAa,EAAEiB,KAAa,EAAEE,GAAa,EAAE;IACpD,IAAInB,KAAK,IAAI,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;MAC1B,IAAIkB,GAAG,IAAI,IAAI,IAAI,IAAI,CAACC,MAAM,CAACC,KAAK,CAACJ,KAAK,EAAEjE,KAAK,CAACsE,KAAK,CAAC,IAAI,IAAI,EAAE;QAChE,MAAM1D,IAAI,GAAG,IAAI,CAACwD,MAAM,CAACG,MAAM,CAAC,IAAI,CAACC,OAAO,CAACrD,YAAY,CAACH,QAAQ,CAAC;QACnE,IAAI,CAACyD,WAAW,CAAC7D,IAAI,CAAC;QACtB,IAAIuD,GAAG,IAAI,IAAI,IAAIF,KAAK,CAACS,QAAQ,CAAC,IAAI,CAAC,EAAE;UACvC9D,IAAI,CAACsD,QAAQ,CAAC,CAAC,EAAED,KAAK,CAACU,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAER,GAAG,CAAC;QAC3C,CAAC,MAAM;UACLvD,IAAI,CAACsD,QAAQ,CAAC,CAAC,EAAED,KAAK,EAAEE,GAAG,CAAC;QAC9B;MACF,CAAC,MAAM;QACL,MAAMS,KAAK,GAAG,IAAI,CAACR,MAAM,CAACG,MAAM,CAACN,KAAK,EAAEE,GAAG,CAAC;QAC5C,IAAI,CAACM,WAAW,CAACG,KAAK,CAAC;MACzB;IACF,CAAC,MAAM;MACL,KAAK,CAACV,QAAQ,CAAClB,KAAK,EAAEiB,KAAK,EAAEE,GAAG,CAAC;IACnC;IACA,IAAI,CAACxC,QAAQ,CAAC,CAAC;EACjB;EAEAkD,YAAYA,CAACjE,IAAU,EAAE0C,GAAiB,EAAE;IAC1C,IAAI1C,IAAI,CAAC4D,OAAO,CAACM,KAAK,KAAK9E,KAAK,CAAC+E,WAAW,EAAE;MAC5C,MAAMC,OAAO,GAAG,IAAI,CAACZ,MAAM,CAACG,MAAM,CAChC,IAAI,CAACC,OAAO,CAACrD,YAAY,CAACH,QAC5B,CAAW;MACXgE,OAAO,CAACP,WAAW,CAAC7D,IAAI,CAAC;MACzB,KAAK,CAACiE,YAAY,CAACG,OAAO,EAAE1B,GAAG,CAAC;IAClC,CAAC,MAAM;MACL,KAAK,CAACuB,YAAY,CAACjE,IAAI,EAAE0C,GAAG,CAAC;IAC/B;EACF;EAEA2B,cAAcA,CAACjC,KAAa,EAAEkC,KAAY,EAAE;IAC1C,MAAMC,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CF,KAAK,CAACG,MAAM,CAAC,IAAInF,KAAK,CAAC,CAAC,CAACoF,MAAM,CAAC,IAAI,CAAC,CACvC,CAAC;IACD,MAAMjC,IAAI,GAAG8B,YAAY,CAACI,GAAG,CAAC,CAAC;IAC/B,IAAIlC,IAAI,IAAI,IAAI,EAAE;IAElB,IAAI,CAACrB,UAAU,CAAC,CAAC;IAEjB,MAAMkB,KAAK,GAAGiC,YAAY,CAACK,KAAK,CAAC,CAAC;IAClC,IAAItC,KAAK,EAAE;MACT,MAAMuC,uBAAuB,GAC3BvC,KAAK,CAACwC,IAAI,KAAK,OAAO,KACrBxC,KAAK,CAACgC,KAAK,CAACjC,MAAM,CAAC,CAAC,KAAK,CAAC,IACxB,CAAC,IAAI,CAAC0C,UAAU,CAACpF,UAAU,EAAEyC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIA,KAAK,GAAG,IAAI,CAACC,MAAM,CAAC,CAAE,CAAC;MACtE,MAAMiC,KAAK,GACThC,KAAK,CAACwC,IAAI,KAAK,OAAO,GAClBxC,KAAK,CAACgC,KAAK,GACX,IAAIhF,KAAK,CAAC,CAAC,CAACoF,MAAM,CAAC;QAAE,CAACpC,KAAK,CAAC0C,GAAG,GAAG1C,KAAK,CAACe;MAAM,CAAC,CAAC;MACtD4B,oBAAoB,CAAC,IAAI,EAAE7C,KAAK,EAAEkC,KAAK,CAAC;MACxC,MAAMY,iBAAiB,GAAG5C,KAAK,CAACwC,IAAI,KAAK,OAAO,GAAG,CAAC,GAAG,CAAC;MACxD,MAAMK,YAAY,GAAG/C,KAAK,GAAGkC,KAAK,CAACjC,MAAM,CAAC,CAAC,GAAG6C,iBAAiB;MAC/D,IAAIL,uBAAuB,EAAE;QAC3B,IAAI,CAACvB,QAAQ,CAAC6B,YAAY,GAAG,CAAC,EAAE,IAAI,CAAC;MACvC;MAEA,MAAMC,OAAO,GAAGxF,aAAa,CAAC,IAAI,CAAC4C,IAAI,CAACJ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MAClD,MAAMiD,UAAU,GAAG9F,YAAY,CAAC+F,IAAI,CAACF,OAAO,EAAE9C,KAAK,CAAC+C,UAAU,CAAC,IAAI,CAAC,CAAC;MACrEE,MAAM,CAACC,IAAI,CAACH,UAAU,CAAC,CAACI,OAAO,CAAEC,IAAI,IAAK;QACxC,IAAI,CAACvC,QAAQ,CAACgC,YAAY,GAAG,CAAC,EAAE,CAAC,EAAEO,IAAI,EAAEL,UAAU,CAACK,IAAI,CAAC,CAAC;MAC5D,CAAC,CAAC;MAEFtD,KAAK,GAAG+C,YAAY;IACtB;IAEA,IAAI,CAACQ,OAAO,EAAEC,aAAa,CAAC,GAAG,IAAI,CAACjD,QAAQ,CAACkD,IAAI,CAACzD,KAAK,CAAC;IACxD,IAAImC,YAAY,CAAClC,MAAM,EAAE;MACvB,IAAIsD,OAAO,EAAE;QACXA,OAAO,GAAGA,OAAO,CAACG,KAAK,CAACF,aAAa,CAAC;QACtCA,aAAa,GAAG,CAAC;MACnB;MAEArB,YAAY,CAACkB,OAAO,CAAEM,WAAW,IAAK;QACpC,IAAIA,WAAW,CAACjB,IAAI,KAAK,OAAO,EAAE;UAChC,MAAMkB,KAAK,GAAG,IAAI,CAACC,WAAW,CAC5BF,WAAW,CAACV,UAAU,EACtBM,OAAO,IAAI1C,SACb,CAAC;UACDgC,oBAAoB,CAACe,KAAK,EAAE,CAAC,EAAED,WAAW,CAACzB,KAAK,CAAC;QACnD,CAAC,MAAM;UACL,MAAM4B,UAAU,GAAG,IAAI,CAACvC,MAAM,CAC5BoC,WAAW,CAACf,GAAG,EACfe,WAAW,CAAC1C,KACd,CAAc;UACd,IAAI,CAACY,YAAY,CAACiC,UAAU,EAAEP,OAAO,IAAI1C,SAAS,CAAC;UACnDsC,MAAM,CAACC,IAAI,CAACO,WAAW,CAACV,UAAU,CAAC,CAACI,OAAO,CAAEC,IAAI,IAAK;YACpDQ,UAAU,CAAC9C,MAAM,CAACsC,IAAI,EAAEK,WAAW,CAACV,UAAU,CAACK,IAAI,CAAC,CAAC;UACvD,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;IAEA,IAAIjD,IAAI,CAACqC,IAAI,KAAK,OAAO,IAAIrC,IAAI,CAAC6B,KAAK,CAACjC,MAAM,CAAC,CAAC,EAAE;MAChD,MAAME,MAAM,GAAGoD,OAAO,GAClBA,OAAO,CAACpD,MAAM,CAACoD,OAAO,CAACnC,MAAM,CAAC,GAAGoC,aAAa,GAC9C,IAAI,CAACvD,MAAM,CAAC,CAAC;MACjB4C,oBAAoB,CAAC,IAAI,EAAE1C,MAAM,EAAEE,IAAI,CAAC6B,KAAK,CAAC;IAChD;IAEA,IAAI,CAAC/C,QAAQ,CAAC,CAAC;IACf,IAAI,CAACR,QAAQ,CAAC,CAAC;EACjB;EAEAoF,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAACxF,OAAO,CAACyF,YAAY,CAAC,iBAAiB,CAAC,KAAK,MAAM;EAChE;EAEAC,IAAIA,CAACjE,KAAa,EAA6B;IAC7C,MAAMK,IAAI,GAAG,IAAI,CAAC6D,IAAI,CAAClE,KAAK,CAAC,CAACuC,GAAG,CAAC,CAAC;IACnC,IAAI,CAAClC,IAAI,EAAE;MACT,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IACnB;IAEA,MAAM,CAACzC,IAAI,EAAEuC,MAAM,CAAC,GAAGE,IAAI;IAC3B,OAAOzC,IAAI,YAAYb,QAAQ,GAAG,CAACa,IAAI,EAAEuC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;EAC/D;EAEAC,IAAIA,CAACJ,KAAa,EAAuC;IACvD,IAAIA,KAAK,KAAK,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;MAC3B,OAAO,IAAI,CAACG,IAAI,CAACJ,KAAK,GAAG,CAAC,CAAC;IAC7B;IACA;IACA,OAAO,IAAI,CAAC2C,UAAU,CAAChF,MAAM,EAAEqC,KAAK,CAAC;EACvC;EAEAmE,KAAKA,CAAA,EAA+D;IAAA,IAA9DnE,KAAK,GAAAY,SAAA,CAAAX,MAAA,QAAAW,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC;IAAA,IAAEX,MAAM,GAAAW,SAAA,CAAAX,MAAA,QAAAW,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGwD,MAAM,CAACC,SAAS;IACxC,MAAMC,QAAQ,GAAGA,CACf1G,IAAgB,EAChB2G,SAAiB,EACjBC,UAAkB,KACf;MACH,IAAIL,KAA6B,GAAG,EAAE;MACtC,IAAIM,UAAU,GAAGD,UAAU;MAC3B5G,IAAI,CAAC2C,QAAQ,CAACmE,SAAS,CACrBH,SAAS,EACTC,UAAU,EACV,CAACG,KAAK,EAAEC,UAAU,EAAEC,WAAW,KAAK;QAClC,IAAIlH,MAAM,CAACgH,KAAK,CAAC,EAAE;UACjBR,KAAK,CAACW,IAAI,CAACH,KAAK,CAAC;QACnB,CAAC,MAAM,IAAIA,KAAK,YAAY7H,aAAa,EAAE;UACzCqH,KAAK,GAAGA,KAAK,CAAC9B,MAAM,CAACiC,QAAQ,CAACK,KAAK,EAAEC,UAAU,EAAEH,UAAU,CAAC,CAAC;QAC/D;QACAA,UAAU,IAAII,WAAW;MAC3B,CACF,CAAC;MACD,OAAOV,KAAK;IACd,CAAC;IACD,OAAOG,QAAQ,CAAC,IAAI,EAAEtE,KAAK,EAAEC,MAAM,CAAC;EACtC;EAOAtB,QAAQA,CAAA,EAA+B;IAAA,IAA9BS,SAAS,GAAAwB,SAAA,CAAAX,MAAA,QAAAW,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;IAAA,IAAEmE,OAAO,GAAAnE,SAAA,CAAAX,MAAA,QAAAW,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;IACnC,IAAI,IAAI,CAAClC,KAAK,EAAE;IAChB,KAAK,CAACC,QAAQ,CAACS,SAAS,EAAE2F,OAAO,CAAC;IAClC,IAAI3F,SAAS,CAACa,MAAM,GAAG,CAAC,EAAE;MACxB,IAAI,CAACxB,OAAO,CAACc,IAAI,CAAClC,OAAO,CAACmC,MAAM,CAACwF,eAAe,EAAE5F,SAAS,EAAE2F,OAAO,CAAC;IACvE;EACF;EAEAb,IAAIA,CAAClE,KAAa,EAAE;IAClB,OAAO,KAAK,CAACkE,IAAI,CAAClE,KAAK,CAAC,CAAC2B,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EACrC;EAEAjB,MAAMA,CAAA,EAAG;IACP;EAAA;EAKFrB,MAAMA,CAACD,SAA4C,EAAQ;IACzD,IAAI,IAAI,CAACV,KAAK,EAAE;MACd,IAAIO,KAAK,CAACC,OAAO,CAACE,SAAS,CAAC,EAAE;QAC5B,IAAI,CAACV,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC2D,MAAM,CAACjD,SAAS,CAAC;MAC3C;MACA;IACF;IACA,IAAI6F,MAAqB,GAAG5H,OAAO,CAAC6H,OAAO,CAACC,IAAI;IAChD,IAAI,OAAO/F,SAAS,KAAK,QAAQ,EAAE;MACjC6F,MAAM,GAAG7F,SAAS;IACpB;IACA,IAAI,CAACH,KAAK,CAACC,OAAO,CAACE,SAAS,CAAC,EAAE;MAC7BA,SAAS,GAAG,IAAI,CAACgG,QAAQ,CAACC,WAAW,CAAC,CAAC;IACzC;IACAjG,SAAS,GAAGA,SAAS,CAACkG,MAAM,CAACC,KAAA,IAAgB;MAAA,IAAf;QAAEC;MAAO,CAAC,GAAAD,KAAA;MACtC,MAAM3H,IAAI,GAAG,IAAI,CAAC6F,IAAI,CAAC+B,MAAM,EAAE,IAAI,CAAC;MACpC,OAAO5H,IAAI,IAAI,CAACC,WAAW,CAACD,IAAI,CAAC;IACnC,CAAC,CAAC;IACF,IAAIwB,SAAS,CAACa,MAAM,GAAG,CAAC,EAAE;MACxB,IAAI,CAACxB,OAAO,CAACc,IAAI,CAAClC,OAAO,CAACmC,MAAM,CAACiG,oBAAoB,EAAER,MAAM,EAAE7F,SAAS,CAAC;IAC3E;IACA,KAAK,CAACC,MAAM,CAACD,SAAS,CAACiD,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACpC,IAAIjD,SAAS,CAACa,MAAM,GAAG,CAAC,EAAE;MACxB,IAAI,CAACxB,OAAO,CAACc,IAAI,CAAClC,OAAO,CAACmC,MAAM,CAACkG,aAAa,EAAET,MAAM,EAAE7F,SAAS,CAAC;IACpE;EACF;EAEAuG,aAAaA,CAAC3F,KAAa,EAAE4C,GAAW,EAAE/C,MAAe,EAAE;IACzD;IACA;IACA,MAAM,CAACjC,IAAI,CAAC,GAAG,IAAI,CAAC+E,UAAU,CAAEiD,CAAO,IAAKA,CAAC,YAAYrI,UAAU,EAAEyC,KAAK,CAAC;IAC3E,IAAIpC,IAAI,IAAIA,IAAI,CAAC4D,OAAO,CAACxD,QAAQ,KAAK4E,GAAG,IAAI/E,WAAW,CAACD,IAAI,CAAC,EAAE;MAC9DA,IAAI,CAACE,aAAa,CAAC+B,MAAM,CAAC;IAC5B;EACF;EAEUd,eAAeA,CAAC8G,KAAgB,EAAE;IAC1CA,KAAK,CAACC,cAAc,CAAC,CAAC;EACxB;EAEQ1D,mBAAmBA,CAACF,KAAY,EAAE;IACxC,MAAMC,YAA2B,GAAG,EAAE;IAEtC,IAAI4D,iBAAiB,GAAG,IAAI7I,KAAK,CAAC,CAAC;IACnCgF,KAAK,CAACmB,OAAO,CAAE2C,EAAE,IAAK;MACpB,MAAM1D,MAAM,GAAG0D,EAAE,EAAE1D,MAAM;MACzB,IAAI,CAACA,MAAM,EAAE;MACb,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;QAC9B,MAAM2D,QAAQ,GAAG3D,MAAM,CAACoB,KAAK,CAAC,IAAI,CAAC;QACnCuC,QAAQ,CAACtE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC0B,OAAO,CAAE6C,IAAI,IAAK;UACtCH,iBAAiB,CAACzD,MAAM,CAAC4D,IAAI,EAAEF,EAAE,CAAC/C,UAAU,CAAC;UAC7Cd,YAAY,CAAC2C,IAAI,CAAC;YAChBpC,IAAI,EAAE,OAAO;YACbR,KAAK,EAAE6D,iBAAiB;YACxB9C,UAAU,EAAE+C,EAAE,CAAC/C,UAAU,IAAI,CAAC;UAChC,CAAC,CAAC;UACF8C,iBAAiB,GAAG,IAAI7I,KAAK,CAAC,CAAC;QACjC,CAAC,CAAC;QACF,MAAMmD,IAAI,GAAG4F,QAAQ,CAACA,QAAQ,CAAChG,MAAM,GAAG,CAAC,CAAC;QAC1C,IAAII,IAAI,EAAE;UACR0F,iBAAiB,CAACzD,MAAM,CAACjC,IAAI,EAAE2F,EAAE,CAAC/C,UAAU,CAAC;QAC/C;MACF,CAAC,MAAM;QACL,MAAML,GAAG,GAAGO,MAAM,CAACC,IAAI,CAACd,MAAM,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,CAACM,GAAG,EAAE;QACV,IAAI,IAAI,CAACvB,KAAK,CAACuB,GAAG,EAAE5F,KAAK,CAACmJ,MAAM,CAAC,EAAE;UACjCJ,iBAAiB,CAACjB,IAAI,CAACkB,EAAE,CAAC;QAC5B,CAAC,MAAM;UACL,IAAID,iBAAiB,CAAC9F,MAAM,CAAC,CAAC,EAAE;YAC9BkC,YAAY,CAAC2C,IAAI,CAAC;cAChBpC,IAAI,EAAE,OAAO;cACbR,KAAK,EAAE6D,iBAAiB;cACxB9C,UAAU,EAAE,CAAC;YACf,CAAC,CAAC;UACJ;UACA8C,iBAAiB,GAAG,IAAI7I,KAAK,CAAC,CAAC;UAC/BiF,YAAY,CAAC2C,IAAI,CAAC;YAChBpC,IAAI,EAAE,YAAY;YAClBE,GAAG;YACH3B,KAAK,EAAEqB,MAAM,CAACM,GAAG,CAAC;YAClBK,UAAU,EAAE+C,EAAE,CAAC/C,UAAU,IAAI,CAAC;UAChC,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC;IAEF,IAAI8C,iBAAiB,CAAC9F,MAAM,CAAC,CAAC,EAAE;MAC9BkC,YAAY,CAAC2C,IAAI,CAAC;QAChBpC,IAAI,EAAE,OAAO;QACbR,KAAK,EAAE6D,iBAAiB;QACxB9C,UAAU,EAAE,CAAC;MACf,CAAC,CAAC;IACJ;IAEA,OAAOd,YAAY;EACrB;EAEQ0B,WAAWA,CAACZ,UAAwB,EAAEM,OAAc,EAAE;IAC5D,IAAIvF,QAA4B;IAChC,MAAMgF,OAAqB,GAAG,CAAC,CAAC;IAEhCG,MAAM,CAACiD,OAAO,CAACnD,UAAU,CAAC,CAACI,OAAO,CAACgD,KAAA,IAAkB;MAAA,IAAjB,CAACzD,GAAG,EAAE3B,KAAK,CAAC,GAAAoF,KAAA;MAC9C,MAAMC,WAAW,GAAG,IAAI,CAACjF,KAAK,CAACuB,GAAG,EAAE5F,KAAK,CAACsE,KAAK,GAAGtE,KAAK,CAACuJ,IAAI,CAAC,IAAI,IAAI;MACrE,IAAID,WAAW,EAAE;QACftI,QAAQ,GAAG4E,GAAG;MAChB,CAAC,MAAM;QACLI,OAAO,CAACJ,GAAG,CAAC,GAAG3B,KAAK;MACtB;IACF,CAAC,CAAC;IAEF,MAAM2C,KAAK,GAAG,IAAI,CAACrC,MAAM,CACvBvD,QAAQ,IAAI,IAAI,CAACwD,OAAO,CAACrD,YAAY,CAACH,QAAQ,EAC9CA,QAAQ,GAAGiF,UAAU,CAACjF,QAAQ,CAAC,GAAG6C,SACpC,CAAe;IAEf,IAAI,CAACgB,YAAY,CAAC+B,KAAK,EAAEL,OAAO,IAAI1C,SAAS,CAAC;IAE9C,MAAMZ,MAAM,GAAG2D,KAAK,CAAC3D,MAAM,CAAC,CAAC;IAC7BkD,MAAM,CAACiD,OAAO,CAACpD,OAAO,CAAC,CAACK,OAAO,CAACmD,KAAA,IAAkB;MAAA,IAAjB,CAAC5D,GAAG,EAAE3B,KAAK,CAAC,GAAAuF,KAAA;MAC3C5C,KAAK,CAAC7C,QAAQ,CAAC,CAAC,EAAEd,MAAM,EAAE2C,GAAG,EAAE3B,KAAK,CAAC;IACvC,CAAC,CAAC;IAEF,OAAO2C,KAAK;EACd;AACF;AAEA,SAASf,oBAAoBA,CAC3B4D,MAAkB,EAClBzG,KAAa,EACb0G,cAAqB,EACrB;EACAA,cAAc,CAACC,MAAM,CAAC,CAAC3G,KAAK,EAAEgG,EAAE,KAAK;IACnC,MAAM/F,MAAM,GAAG7C,EAAE,CAAC6C,MAAM,CAAC+F,EAAE,CAAC;IAC5B,IAAI/C,UAAU,GAAG+C,EAAE,CAAC/C,UAAU,IAAI,CAAC,CAAC;IACpC,IAAI+C,EAAE,CAAC1D,MAAM,IAAI,IAAI,EAAE;MACrB,IAAI,OAAO0D,EAAE,CAAC1D,MAAM,KAAK,QAAQ,EAAE;QACjC,MAAM4D,IAAI,GAAGF,EAAE,CAAC1D,MAAM;QACtBmE,MAAM,CAACvF,QAAQ,CAAClB,KAAK,EAAEkG,IAAI,CAAC;QAC5B,MAAM,CAACjC,IAAI,CAAC,GAAGwC,MAAM,CAAC9D,UAAU,CAAC5F,QAAQ,EAAEiD,KAAK,CAAC;QACjD,MAAMgD,OAAO,GAAGxF,aAAa,CAACyG,IAAI,CAAC;QACnChB,UAAU,GAAG9F,YAAY,CAAC+F,IAAI,CAACF,OAAO,EAAEC,UAAU,CAAC,IAAI,CAAC,CAAC;MAC3D,CAAC,MAAM,IAAI,OAAO+C,EAAE,CAAC1D,MAAM,KAAK,QAAQ,EAAE;QACxC,MAAMM,GAAG,GAAGO,MAAM,CAACC,IAAI,CAAC4C,EAAE,CAAC1D,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,IAAIM,GAAG,IAAI,IAAI,EAAE,OAAO5C,KAAK;QAC7ByG,MAAM,CAACvF,QAAQ,CAAClB,KAAK,EAAE4C,GAAG,EAAEoD,EAAE,CAAC1D,MAAM,CAACM,GAAG,CAAC,CAAC;QAC3C,MAAMgE,aAAa,GAAGH,MAAM,CAACrF,MAAM,CAACC,KAAK,CAACuB,GAAG,EAAE5F,KAAK,CAACmJ,MAAM,CAAC,IAAI,IAAI;QACpE,IAAIS,aAAa,EAAE;UACjB,MAAM,CAAC3C,IAAI,CAAC,GAAGwC,MAAM,CAAC9D,UAAU,CAAC5F,QAAQ,EAAEiD,KAAK,CAAC;UACjD,MAAMgD,OAAO,GAAGxF,aAAa,CAACyG,IAAI,CAAC;UACnChB,UAAU,GAAG9F,YAAY,CAAC+F,IAAI,CAACF,OAAO,EAAEC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC3D;MACF;IACF;IACAE,MAAM,CAACC,IAAI,CAACH,UAAU,CAAC,CAACI,OAAO,CAAET,GAAG,IAAK;MACvC6D,MAAM,CAAC1F,QAAQ,CAACf,KAAK,EAAEC,MAAM,EAAE2C,GAAG,EAAEK,UAAU,CAACL,GAAG,CAAC,CAAC;IACtD,CAAC,CAAC;IACF,OAAO5C,KAAK,GAAGC,MAAM;EACvB,CAAC,EAAED,KAAK,CAAC;AACX;AAEA,eAAejC,MAAM", "ignoreList": []}