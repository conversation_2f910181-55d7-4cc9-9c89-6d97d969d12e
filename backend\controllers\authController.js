const User = require('../models/User');
const Agreement = require('../models/Agreement');
const jwt = require('jsonwebtoken');
const nodemailer = require('nodemailer');

// Email transporter setup
const transporter = nodemailer.createTransport({
  service: 'gmail',
  host: 'smtp.gmail.com',
  port: 587,
  secure: false,
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS
  },
  tls: {
    rejectUnauthorized: false
  }
});

// Generate OTP
const generateOTP = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Send OTP via email
const sendOTP = async (email, otp) => {
  try {
    console.log('Attempting to send email to:', email);
    console.log('Using email user:', process.env.EMAIL_USER);

    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: email,
      subject: 'Email Verification OTP',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Email Verification</h2>
          <p>Your OTP for email verification is:</p>
          <div style="background-color: #f0f0f0; padding: 20px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 5px; margin: 20px 0;">
            ${otp}
          </div>
          <p>This OTP will expire in 10 minutes.</p>
          <p>If you didn't request this verification, please ignore this email.</p>
        </div>
      `
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('Email sent successfully:', result.messageId);
    return result;
  } catch (error) {
    console.error('Email sending failed:', error);
    throw new Error(`Failed to send email: ${error.message}`);
  }
};

// Send agreement link
const sendAgreementLink = async (email, token) => {
  const agreementLink = `${process.env.FRONTEND_URL}/sign-agreement/${token}`;

  const mailOptions = {
    from: process.env.EMAIL_USER,
    to: email,
    subject: 'Sign Agreement',
    html: `<p>Please sign the agreement by clicking on this link: <a href="${agreementLink}">Sign Agreement</a></p>`
  };

  await transporter.sendMail(mailOptions);
};

// Register user
exports.register = async (req, res) => {
  try {
    console.log('Registration request received:', req.body);
    const { name, email, mobileNumber, alternativeMobileNumber, password } = req.body;

    // Check if user already exists
    console.log('Checking if user exists with email:', email);
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      console.log('User already exists');
      return res.status(400).json({ message: 'User already exists' });
    }
    console.log('User does not exist, proceeding with registration');

    // Generate OTP
    console.log('Generating OTP...');
    const otp = generateOTP();
    console.log('Generated OTP:', otp);
    const otpExpiry = new Date();
    otpExpiry.setMinutes(otpExpiry.getMinutes() + 10);
    console.log('OTP expiry set to:', otpExpiry); // OTP valid for 10 minutes

    // Create new user
    console.log('Creating new user...');
    const newUser = new User({
      name,
      email,
      mobileNumber,
      alternativeMobileNumber,
      password, // Plain text as required
      otp: {
        code: otp,
        expiresAt: otpExpiry
      }
    });

    console.log('Saving user to database...');
    await newUser.save();
    console.log('User saved successfully with ID:', newUser._id);

    // Send OTP
    if (process.env.SKIP_EMAIL === 'true') {
      console.log('Email sending skipped (SKIP_EMAIL=true). OTP for testing:', otp);
    } else {
      console.log('Sending OTP email to:', email);
      try {
        await sendOTP(email, otp);
        console.log('OTP email sent successfully');
      } catch (emailError) {
        console.error('Email sending failed, but user was created:', emailError.message);
        console.log('OTP for manual verification:', otp);
        // Continue with registration even if email fails
      }
    }

    res.status(201).json({ message: 'User registered. Please verify your email.' });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Verify OTP
exports.verifyOTP = async (req, res) => {
  try {
    console.log('OTP verification request received:', req.body);
    const { email, otp } = req.body;

    console.log('Looking for user with email:', email);
    const user = await User.findOne({ email });
    if (!user) {
      console.log('User not found for email:', email);
      return res.status(404).json({ message: 'User not found' });
    }

    console.log('User found. Stored OTP:', user.otp?.code, 'Provided OTP:', otp);
    if (!user.otp || user.otp.code !== otp) {
      console.log('Invalid OTP provided');
      return res.status(400).json({ message: 'Invalid OTP' });
    }

    console.log('OTP expiry:', user.otp.expiresAt, 'Current time:', new Date());
    if (new Date() > user.otp.expiresAt) {
      console.log('OTP has expired');
      return res.status(400).json({ message: 'OTP expired' });
    }

    console.log('OTP verified successfully. Updating user...');
    // Update user verification status
    user.isVerified = true;
    user.otp = undefined;
    await user.save();
    console.log('User verification status updated');

    // Generate token for agreement signing
    const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, { expiresIn: '1d' });
    console.log('Agreement token generated:', token.substring(0, 20) + '...');

    // Send agreement link
    if (process.env.SKIP_EMAIL === 'true') {
      console.log('Email sending skipped. Agreement link would be:', `${process.env.FRONTEND_URL}/sign-agreement/${token}`);
    } else {
      try {
        await sendAgreementLink(email, token);
        console.log('Agreement link sent successfully');
      } catch (emailError) {
        console.error('Failed to send agreement link:', emailError.message);
        console.log('Agreement link for manual access:', `${process.env.FRONTEND_URL}/sign-agreement/${token}`);
      }
    }

    res.status(200).json({
      message: 'Email verified. Please sign the agreement.',
      agreementLink: `${process.env.FRONTEND_URL}/sign-agreement/${token}`
    });
  } catch (error) {
    console.error('OTP verification error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Login
exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;

    const user = await User.findOne({ email });
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    if (user.password !== password) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    if (!user.isVerified) {
      // Generate new OTP
      const otp = generateOTP();
      const otpExpiry = new Date();
      otpExpiry.setMinutes(otpExpiry.getMinutes() + 10);

      user.otp = {
        code: otp,
        expiresAt: otpExpiry
      };
      await user.save();

      // Send OTP with email skip logic
      if (process.env.SKIP_EMAIL === 'true') {
        console.log('Email sending skipped. OTP for verification:', otp);
      } else {
        try {
          await sendOTP(email, otp);
        } catch (emailError) {
          console.error('Failed to send OTP:', emailError.message);
          console.log('OTP for manual verification:', otp);
        }
      }

      return res.status(403).json({ message: 'Email not verified', requiresVerification: true });
    }

    if (!user.isSignedAgreement) {
      // Generate token for agreement signing
      const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, { expiresIn: '1d' });

      // Send agreement link with email skip logic
      if (process.env.SKIP_EMAIL === 'true') {
        console.log('Email sending skipped. Agreement link:', `${process.env.FRONTEND_URL}/sign-agreement/${token}`);
      } else {
        try {
          await sendAgreementLink(email, token);
        } catch (emailError) {
          console.error('Failed to send agreement link:', emailError.message);
          console.log('Agreement link for manual access:', `${process.env.FRONTEND_URL}/sign-agreement/${token}`);
        }
      }

      return res.status(403).json({
        message: 'Agreement not signed',
        requiresAgreement: true,
        agreementLink: `${process.env.FRONTEND_URL}/sign-agreement/${token}`
      });
    }

    // Generate JWT token
    const token = jwt.sign(
      { userId: user._id, role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );

    res.status(200).json({
      token,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role
      }
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Admin login
exports.adminLogin = async (req, res) => {
  try {
    const { email, password } = req.body;

    const admin = await User.findOne({ email, role: 'admin' });
    if (!admin) {
      return res.status(404).json({ message: 'Admin not found' });
    }

    if (admin.password !== password) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Generate JWT token
    const token = jwt.sign(
      { userId: admin._id, role: admin.role },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );

    res.status(200).json({
      token,
      user: {
        id: admin._id,
        name: admin.name,
        email: admin.email,
        role: admin.role
      }
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Sign agreement
exports.signAgreement = async (req, res) => {
  try {
    const { signature } = req.body;
    const userId = req.user.userId;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    user.signature = signature;
    user.isSignedAgreement = true;
    await user.save();

    // Generate JWT token
    const token = jwt.sign(
      { userId: user._id, role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );

    res.status(200).json({
      message: 'Agreement signed successfully',
      token,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role
      }
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get agreement
exports.getAgreement = async (req, res) => {
  try {
    // Get the latest agreement
    const agreement = await Agreement.findOne().sort({ version: -1 });

    if (!agreement) {
      return res.status(404).json({ message: 'Agreement not found' });
    }

    res.status(200).json({ agreement });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};