{"version": 3, "file": "core.js", "names": ["<PERSON><PERSON><PERSON>", "Parchment", "Range", "Block", "BlockEmbed", "Break", "Container", "<PERSON><PERSON><PERSON>", "Embed", "Inline", "<PERSON><PERSON>", "TextBlot", "Clipboard", "History", "Keyboard", "Uploader", "Delta", "Op", "OpIterator", "AttributeMap", "Input", "UINode", "default", "<PERSON><PERSON><PERSON>", "register"], "sources": ["../src/core.ts"], "sourcesContent": ["import Quill, { Parchment, Range } from './core/quill.js';\nimport type {\n  Bounds,\n  DebugLevel,\n  EmitterSource,\n  ExpandedQuillOptions,\n  QuillOptions,\n} from './core/quill.js';\n\nimport Block, { BlockEmbed } from './blots/block.js';\nimport Break from './blots/break.js';\nimport Container from './blots/container.js';\nimport Cursor from './blots/cursor.js';\nimport Embed from './blots/embed.js';\nimport Inline from './blots/inline.js';\nimport Scroll from './blots/scroll.js';\nimport TextBlot from './blots/text.js';\n\nimport Clipboard from './modules/clipboard.js';\nimport History from './modules/history.js';\nimport Keyboard from './modules/keyboard.js';\nimport Uploader from './modules/uploader.js';\nimport Delta, { Op, OpIterator, AttributeMap } from 'quill-delta';\nimport Input from './modules/input.js';\nimport UINode from './modules/uiNode.js';\n\nexport { default as Module } from './core/module.js';\nexport { Delta, Op, OpIterator, AttributeMap, Parchment, Range };\nexport type {\n  Bounds,\n  DebugLevel,\n  EmitterSource,\n  ExpandedQuillOptions,\n  QuillOptions,\n};\n\nQuill.register({\n  'blots/block': Block,\n  'blots/block/embed': BlockEmbed,\n  'blots/break': Break,\n  'blots/container': Container,\n  'blots/cursor': Cursor,\n  'blots/embed': Embed,\n  'blots/inline': Inline,\n  'blots/scroll': Scroll,\n  'blots/text': TextBlot,\n\n  'modules/clipboard': Clipboard,\n  'modules/history': History,\n  'modules/keyboard': Keyboard,\n  'modules/uploader': Uploader,\n  'modules/input': Input,\n  'modules/uiNode': UINode,\n});\n\nexport default Quill;\n"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,KAAK,QAAQ,iBAAiB;AASzD,OAAOC,KAAK,IAAIC,UAAU,QAAQ,kBAAkB;AACpD,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,QAAQ,MAAM,iBAAiB;AAEtC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,KAAK,IAAIC,EAAE,EAAEC,UAAU,EAAEC,YAAY,QAAQ,aAAa;AACjE,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,MAAM,MAAM,qBAAqB;AAExC,SAASC,OAAO,IAAIC,MAAM,QAAQ,kBAAkB;AACpD,SAASP,KAAK,EAAEC,EAAE,EAAEC,UAAU,EAAEC,YAAY,EAAElB,SAAS,EAAEC,KAAK;AAS9DF,KAAK,CAACwB,QAAQ,CAAC;EACb,aAAa,EAAErB,KAAK;EACpB,mBAAmB,EAAEC,UAAU;EAC/B,aAAa,EAAEC,KAAK;EACpB,iBAAiB,EAAEC,SAAS;EAC5B,cAAc,EAAEC,MAAM;EACtB,aAAa,EAAEC,KAAK;EACpB,cAAc,EAAEC,MAAM;EACtB,cAAc,EAAEC,MAAM;EACtB,YAAY,EAAEC,QAAQ;EAEtB,mBAAmB,EAAEC,SAAS;EAC9B,iBAAiB,EAAEC,OAAO;EAC1B,kBAAkB,EAAEC,QAAQ;EAC5B,kBAAkB,EAAEC,QAAQ;EAC5B,eAAe,EAAEK,KAAK;EACtB,gBAAgB,EAAEC;AACpB,CAAC,CAAC;AAEF,eAAerB,KAAK", "ignoreList": []}