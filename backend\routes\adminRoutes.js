const express = require('express');
const router = express.Router();
const adminController = require('../controllers/adminController');
const { verifyToken, isAdmin } = require('../middleware/auth');

// All admin routes require authentication and admin privileges
router.use(verifyToken);
router.use(isAdmin);

// Dashboard stats
router.get('/dashboard-stats', adminController.getDashboardStats);

// User management routes
router.get('/users', adminController.getAllUsers);
router.post('/users', adminController.createUser);
router.put('/users/:userId', adminController.updateUser);
router.delete('/users/:userId', adminController.deleteUser);
router.post('/users/:userId/send-otp', adminController.sendOTPManually);
router.post('/users/:userId/send-agreement', adminController.sendAgreementLinkManually);

// Agreement management routes
router.get('/agreements', adminController.getAllAgreements);
router.post('/agreements', adminController.createAgreement);
router.put('/agreements/:agreementId', adminController.updateAgreement);
router.delete('/agreements/:agreementId', adminController.deleteAgreement);

module.exports = router;
