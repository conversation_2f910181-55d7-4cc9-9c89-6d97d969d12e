{"version": 3, "file": "strike.js", "names": ["Bold", "Strike", "blotName", "tagName"], "sources": ["../../src/formats/strike.ts"], "sourcesContent": ["import Bold from './bold.js';\n\nclass Strike extends Bold {\n  static blotName = 'strike';\n  static tagName = ['S', 'STRIKE'];\n}\n\nexport default Strike;\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,WAAW;AAE5B,MAAMC,MAAM,SAASD,IAAI,CAAC;EACxB,OAAOE,QAAQ,GAAG,QAAQ;EAC1B,OAAOC,OAAO,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC;AAClC;AAEA,eAAeF,MAAM", "ignoreList": []}