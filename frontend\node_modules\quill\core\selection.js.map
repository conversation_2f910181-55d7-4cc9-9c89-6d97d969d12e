{"version": 3, "file": "selection.js", "names": ["LeafBlot", "<PERSON><PERSON>", "cloneDeep", "isEqual", "Emitter", "logger", "debug", "Range", "constructor", "index", "length", "arguments", "undefined", "Selection", "scroll", "emitter", "composing", "mouseDown", "root", "domNode", "cursor", "create", "savedRange", "<PERSON><PERSON><PERSON><PERSON>", "lastNative", "handleComposition", "handleDragging", "listenDOM", "document", "setTimeout", "update", "bind", "sources", "USER", "on", "events", "SCROLL_BEFORE_UPDATE", "hasFocus", "native", "getNativeRange", "start", "node", "textNode", "once", "SCROLL_UPDATE", "source", "mutations", "contains", "end", "setNativeRange", "offset", "triggeredByTyping", "some", "mutation", "type", "target", "SILENT", "ignored", "SCROLL_OPTIMIZE", "context", "range", "startNode", "startOffset", "endNode", "endOffset", "COMPOSITION_BEFORE_START", "COMPOSITION_END", "parent", "restore", "body", "focus", "preventScroll", "setRang<PERSON>", "format", "value", "nativeRange", "collapsed", "query", "BLOCK", "blot", "find", "after", "split", "insertBefore", "attach", "optimize", "data", "getBounds", "<PERSON><PERSON><PERSON><PERSON>", "Math", "min", "leaf", "next", "line", "nextLine", "position", "createRange", "setStart", "setEnd", "getBoundingClientRect", "side", "rect", "Text", "Element", "bottom", "top", "height", "left", "right", "width", "selection", "getSelection", "rangeCount", "getRangeAt", "normalizeNative", "info", "getRange", "isConnected", "normalized", "normalizedToRange", "activeElement", "positions", "push", "indexes", "map", "max", "startContainer", "endContainer", "for<PERSON>ach", "childNodes", "<PERSON><PERSON><PERSON><PERSON>", "rangeToNative", "getPosition", "inclusive", "leafOffset", "force", "parentNode", "tagName", "Array", "from", "indexOf", "removeAllRanges", "addRange", "blur", "API", "args", "oldRange", "SELECTION_CHANGE", "emit", "EDITOR_CHANGE", "descendant", "e"], "sources": ["../../src/core/selection.ts"], "sourcesContent": ["import { LeafBlot, Scope } from 'parchment';\nimport { cloneDeep, isEqual } from 'lodash-es';\nimport Emitter from './emitter.js';\nimport type { EmitterSource } from './emitter.js';\nimport logger from './logger.js';\nimport type Cursor from '../blots/cursor.js';\nimport type Scroll from '../blots/scroll.js';\n\nconst debug = logger('quill:selection');\n\ntype NativeRange = AbstractRange;\n\ninterface NormalizedRange {\n  start: {\n    node: NativeRange['startContainer'];\n    offset: NativeRange['startOffset'];\n  };\n  end: { node: NativeRange['endContainer']; offset: NativeRange['endOffset'] };\n  native: NativeRange;\n}\n\nexport interface Bounds {\n  bottom: number;\n  height: number;\n  left: number;\n  right: number;\n  top: number;\n  width: number;\n}\n\nexport class Range {\n  constructor(\n    public index: number,\n    public length = 0,\n  ) {}\n}\n\nclass Selection {\n  scroll: Scroll;\n  emitter: Emitter;\n  composing: boolean;\n  mouseDown: boolean;\n\n  root: HTMLElement;\n  cursor: Cursor;\n  savedRange: Range;\n  lastRange: Range | null;\n  lastNative: NormalizedRange | null;\n\n  constructor(scroll: Scroll, emitter: Emitter) {\n    this.emitter = emitter;\n    this.scroll = scroll;\n    this.composing = false;\n    this.mouseDown = false;\n    this.root = this.scroll.domNode;\n    // @ts-expect-error\n    this.cursor = this.scroll.create('cursor', this);\n    // savedRange is last non-null range\n    this.savedRange = new Range(0, 0);\n    this.lastRange = this.savedRange;\n    this.lastNative = null;\n    this.handleComposition();\n    this.handleDragging();\n    this.emitter.listenDOM('selectionchange', document, () => {\n      if (!this.mouseDown && !this.composing) {\n        setTimeout(this.update.bind(this, Emitter.sources.USER), 1);\n      }\n    });\n    this.emitter.on(Emitter.events.SCROLL_BEFORE_UPDATE, () => {\n      if (!this.hasFocus()) return;\n      const native = this.getNativeRange();\n      if (native == null) return;\n      if (native.start.node === this.cursor.textNode) return; // cursor.restore() will handle\n      this.emitter.once(\n        Emitter.events.SCROLL_UPDATE,\n        (source, mutations: MutationRecord[]) => {\n          try {\n            if (\n              this.root.contains(native.start.node) &&\n              this.root.contains(native.end.node)\n            ) {\n              this.setNativeRange(\n                native.start.node,\n                native.start.offset,\n                native.end.node,\n                native.end.offset,\n              );\n            }\n            const triggeredByTyping = mutations.some(\n              (mutation) =>\n                mutation.type === 'characterData' ||\n                mutation.type === 'childList' ||\n                (mutation.type === 'attributes' &&\n                  mutation.target === this.root),\n            );\n            this.update(triggeredByTyping ? Emitter.sources.SILENT : source);\n          } catch (ignored) {\n            // ignore\n          }\n        },\n      );\n    });\n    this.emitter.on(Emitter.events.SCROLL_OPTIMIZE, (mutations, context) => {\n      if (context.range) {\n        const { startNode, startOffset, endNode, endOffset } = context.range;\n        this.setNativeRange(startNode, startOffset, endNode, endOffset);\n        this.update(Emitter.sources.SILENT);\n      }\n    });\n    this.update(Emitter.sources.SILENT);\n  }\n\n  handleComposition() {\n    this.emitter.on(Emitter.events.COMPOSITION_BEFORE_START, () => {\n      this.composing = true;\n    });\n    this.emitter.on(Emitter.events.COMPOSITION_END, () => {\n      this.composing = false;\n      if (this.cursor.parent) {\n        const range = this.cursor.restore();\n        if (!range) return;\n        setTimeout(() => {\n          this.setNativeRange(\n            range.startNode,\n            range.startOffset,\n            range.endNode,\n            range.endOffset,\n          );\n        }, 1);\n      }\n    });\n  }\n\n  handleDragging() {\n    this.emitter.listenDOM('mousedown', document.body, () => {\n      this.mouseDown = true;\n    });\n    this.emitter.listenDOM('mouseup', document.body, () => {\n      this.mouseDown = false;\n      this.update(Emitter.sources.USER);\n    });\n  }\n\n  focus() {\n    if (this.hasFocus()) return;\n    this.root.focus({ preventScroll: true });\n    this.setRange(this.savedRange);\n  }\n\n  format(format: string, value: unknown) {\n    this.scroll.update();\n    const nativeRange = this.getNativeRange();\n    if (\n      nativeRange == null ||\n      !nativeRange.native.collapsed ||\n      this.scroll.query(format, Scope.BLOCK)\n    )\n      return;\n    if (nativeRange.start.node !== this.cursor.textNode) {\n      const blot = this.scroll.find(nativeRange.start.node, false);\n      if (blot == null) return;\n      // TODO Give blot ability to not split\n      if (blot instanceof LeafBlot) {\n        const after = blot.split(nativeRange.start.offset);\n        blot.parent.insertBefore(this.cursor, after);\n      } else {\n        // @ts-expect-error TODO: nativeRange.start.node doesn't seem to match function signature\n        blot.insertBefore(this.cursor, nativeRange.start.node); // Should never happen\n      }\n      this.cursor.attach();\n    }\n    this.cursor.format(format, value);\n    this.scroll.optimize();\n    this.setNativeRange(this.cursor.textNode, this.cursor.textNode.data.length);\n    this.update();\n  }\n\n  getBounds(index: number, length = 0) {\n    const scrollLength = this.scroll.length();\n    index = Math.min(index, scrollLength - 1);\n    length = Math.min(index + length, scrollLength - 1) - index;\n    let node: Node;\n    let [leaf, offset] = this.scroll.leaf(index);\n    if (leaf == null) return null;\n    if (length > 0 && offset === leaf.length()) {\n      const [next] = this.scroll.leaf(index + 1);\n      if (next) {\n        const [line] = this.scroll.line(index);\n        const [nextLine] = this.scroll.line(index + 1);\n        if (line === nextLine) {\n          leaf = next;\n          offset = 0;\n        }\n      }\n    }\n    [node, offset] = leaf.position(offset, true);\n    const range = document.createRange();\n    if (length > 0) {\n      range.setStart(node, offset);\n      [leaf, offset] = this.scroll.leaf(index + length);\n      if (leaf == null) return null;\n      [node, offset] = leaf.position(offset, true);\n      range.setEnd(node, offset);\n      return range.getBoundingClientRect();\n    }\n    let side: 'left' | 'right' = 'left';\n    let rect: DOMRect;\n    if (node instanceof Text) {\n      // Return null if the text node is empty because it is\n      // not able to get a useful client rect:\n      // https://github.com/w3c/csswg-drafts/issues/2514.\n      // Empty text nodes are most likely caused by TextBlot#optimize()\n      // not getting called when editor content changes.\n      if (!node.data.length) {\n        return null;\n      }\n      if (offset < node.data.length) {\n        range.setStart(node, offset);\n        range.setEnd(node, offset + 1);\n      } else {\n        range.setStart(node, offset - 1);\n        range.setEnd(node, offset);\n        side = 'right';\n      }\n      rect = range.getBoundingClientRect();\n    } else {\n      if (!(leaf.domNode instanceof Element)) return null;\n      rect = leaf.domNode.getBoundingClientRect();\n      if (offset > 0) side = 'right';\n    }\n    return {\n      bottom: rect.top + rect.height,\n      height: rect.height,\n      left: rect[side],\n      right: rect[side],\n      top: rect.top,\n      width: 0,\n    };\n  }\n\n  getNativeRange(): NormalizedRange | null {\n    const selection = document.getSelection();\n    if (selection == null || selection.rangeCount <= 0) return null;\n    const nativeRange = selection.getRangeAt(0);\n    if (nativeRange == null) return null;\n    const range = this.normalizeNative(nativeRange);\n    debug.info('getNativeRange', range);\n    return range;\n  }\n\n  getRange(): [Range, NormalizedRange] | [null, null] {\n    const root = this.scroll.domNode;\n    if ('isConnected' in root && !root.isConnected) {\n      // document.getSelection() forces layout on Blink, so we trend to\n      // not calling it.\n      return [null, null];\n    }\n    const normalized = this.getNativeRange();\n    if (normalized == null) return [null, null];\n    const range = this.normalizedToRange(normalized);\n    return [range, normalized];\n  }\n\n  hasFocus(): boolean {\n    return (\n      document.activeElement === this.root ||\n      (document.activeElement != null &&\n        contains(this.root, document.activeElement))\n    );\n  }\n\n  normalizedToRange(range: NormalizedRange) {\n    const positions: [Node, number][] = [\n      [range.start.node, range.start.offset],\n    ];\n    if (!range.native.collapsed) {\n      positions.push([range.end.node, range.end.offset]);\n    }\n    const indexes = positions.map((position) => {\n      const [node, offset] = position;\n      const blot = this.scroll.find(node, true);\n      // @ts-expect-error Fix me later\n      const index = blot.offset(this.scroll);\n      if (offset === 0) {\n        return index;\n      }\n      if (blot instanceof LeafBlot) {\n        return index + blot.index(node, offset);\n      }\n      // @ts-expect-error Fix me later\n      return index + blot.length();\n    });\n    const end = Math.min(Math.max(...indexes), this.scroll.length() - 1);\n    const start = Math.min(end, ...indexes);\n    return new Range(start, end - start);\n  }\n\n  normalizeNative(nativeRange: NativeRange) {\n    if (\n      !contains(this.root, nativeRange.startContainer) ||\n      (!nativeRange.collapsed && !contains(this.root, nativeRange.endContainer))\n    ) {\n      return null;\n    }\n    const range = {\n      start: {\n        node: nativeRange.startContainer,\n        offset: nativeRange.startOffset,\n      },\n      end: { node: nativeRange.endContainer, offset: nativeRange.endOffset },\n      native: nativeRange,\n    };\n    [range.start, range.end].forEach((position) => {\n      let { node, offset } = position;\n      while (!(node instanceof Text) && node.childNodes.length > 0) {\n        if (node.childNodes.length > offset) {\n          node = node.childNodes[offset];\n          offset = 0;\n        } else if (node.childNodes.length === offset) {\n          // @ts-expect-error Fix me later\n          node = node.lastChild;\n          if (node instanceof Text) {\n            offset = node.data.length;\n          } else if (node.childNodes.length > 0) {\n            // Container case\n            offset = node.childNodes.length;\n          } else {\n            // Embed case\n            offset = node.childNodes.length + 1;\n          }\n        } else {\n          break;\n        }\n      }\n      position.node = node;\n      position.offset = offset;\n    });\n    return range;\n  }\n\n  rangeToNative(range: Range): [Node | null, number, Node | null, number] {\n    const scrollLength = this.scroll.length();\n\n    const getPosition = (\n      index: number,\n      inclusive: boolean,\n    ): [Node | null, number] => {\n      index = Math.min(scrollLength - 1, index);\n      const [leaf, leafOffset] = this.scroll.leaf(index);\n      return leaf ? leaf.position(leafOffset, inclusive) : [null, -1];\n    };\n    return [\n      ...getPosition(range.index, false),\n      ...getPosition(range.index + range.length, true),\n    ];\n  }\n\n  setNativeRange(\n    startNode: Node | null,\n    startOffset?: number,\n    endNode = startNode,\n    endOffset = startOffset,\n    force = false,\n  ) {\n    debug.info('setNativeRange', startNode, startOffset, endNode, endOffset);\n    if (\n      startNode != null &&\n      (this.root.parentNode == null ||\n        startNode.parentNode == null ||\n        // @ts-expect-error Fix me later\n        endNode.parentNode == null)\n    ) {\n      return;\n    }\n    const selection = document.getSelection();\n    if (selection == null) return;\n    if (startNode != null) {\n      if (!this.hasFocus()) this.root.focus({ preventScroll: true });\n      const { native } = this.getNativeRange() || {};\n      if (\n        native == null ||\n        force ||\n        startNode !== native.startContainer ||\n        startOffset !== native.startOffset ||\n        endNode !== native.endContainer ||\n        endOffset !== native.endOffset\n      ) {\n        if (startNode instanceof Element && startNode.tagName === 'BR') {\n          // @ts-expect-error Fix me later\n          startOffset = Array.from(startNode.parentNode.childNodes).indexOf(\n            startNode,\n          );\n          startNode = startNode.parentNode;\n        }\n        if (endNode instanceof Element && endNode.tagName === 'BR') {\n          // @ts-expect-error Fix me later\n          endOffset = Array.from(endNode.parentNode.childNodes).indexOf(\n            endNode,\n          );\n          endNode = endNode.parentNode;\n        }\n        const range = document.createRange();\n        // @ts-expect-error Fix me later\n        range.setStart(startNode, startOffset);\n        // @ts-expect-error Fix me later\n        range.setEnd(endNode, endOffset);\n        selection.removeAllRanges();\n        selection.addRange(range);\n      }\n    } else {\n      selection.removeAllRanges();\n      this.root.blur();\n    }\n  }\n\n  setRange(range: Range | null, force: boolean, source?: EmitterSource): void;\n  setRange(range: Range | null, source?: EmitterSource): void;\n  setRange(\n    range: Range | null,\n    force: boolean | EmitterSource = false,\n    source: EmitterSource = Emitter.sources.API,\n  ): void {\n    if (typeof force === 'string') {\n      source = force;\n      force = false;\n    }\n    debug.info('setRange', range);\n    if (range != null) {\n      const args = this.rangeToNative(range);\n      this.setNativeRange(...args, force);\n    } else {\n      this.setNativeRange(null);\n    }\n    this.update(source);\n  }\n\n  update(source: EmitterSource = Emitter.sources.USER) {\n    const oldRange = this.lastRange;\n    const [lastRange, nativeRange] = this.getRange();\n    this.lastRange = lastRange;\n    this.lastNative = nativeRange;\n    if (this.lastRange != null) {\n      this.savedRange = this.lastRange;\n    }\n    if (!isEqual(oldRange, this.lastRange)) {\n      if (\n        !this.composing &&\n        nativeRange != null &&\n        nativeRange.native.collapsed &&\n        nativeRange.start.node !== this.cursor.textNode\n      ) {\n        const range = this.cursor.restore();\n        if (range) {\n          this.setNativeRange(\n            range.startNode,\n            range.startOffset,\n            range.endNode,\n            range.endOffset,\n          );\n        }\n      }\n      const args = [\n        Emitter.events.SELECTION_CHANGE,\n        cloneDeep(this.lastRange),\n        cloneDeep(oldRange),\n        source,\n      ];\n      this.emitter.emit(Emitter.events.EDITOR_CHANGE, ...args);\n      if (source !== Emitter.sources.SILENT) {\n        this.emitter.emit(...args);\n      }\n    }\n  }\n}\n\nfunction contains(parent: Node, descendant: Node) {\n  try {\n    // Firefox inserts inaccessible nodes around video elements\n    descendant.parentNode; // eslint-disable-line @typescript-eslint/no-unused-expressions\n  } catch (e) {\n    return false;\n  }\n  return parent.contains(descendant);\n}\n\nexport default Selection;\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,KAAK,QAAQ,WAAW;AAC3C,SAASC,SAAS,EAAEC,OAAO,QAAQ,WAAW;AAC9C,OAAOC,OAAO,MAAM,cAAc;AAElC,OAAOC,MAAM,MAAM,aAAa;AAIhC,MAAMC,KAAK,GAAGD,MAAM,CAAC,iBAAiB,CAAC;AAsBvC,OAAO,MAAME,KAAK,CAAC;EACjBC,WAAWA,CACFC,KAAa,EAEpB;IAAA,IADOC,MAAM,GAAAC,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC;IAAA,KADVF,KAAa,GAAbA,KAAa;IAAA,KACbC,MAAM,GAANA,MAAM;EACZ;AACL;AAEA,MAAMG,SAAS,CAAC;EAYdL,WAAWA,CAACM,MAAc,EAAEC,OAAgB,EAAE;IAC5C,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,IAAI,GAAG,IAAI,CAACJ,MAAM,CAACK,OAAO;IAC/B;IACA,IAAI,CAACC,MAAM,GAAG,IAAI,CAACN,MAAM,CAACO,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC;IAChD;IACA,IAAI,CAACC,UAAU,GAAG,IAAIf,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACjC,IAAI,CAACgB,SAAS,GAAG,IAAI,CAACD,UAAU;IAChC,IAAI,CAACE,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,cAAc,CAAC,CAAC;IACrB,IAAI,CAACX,OAAO,CAACY,SAAS,CAAC,iBAAiB,EAAEC,QAAQ,EAAE,MAAM;MACxD,IAAI,CAAC,IAAI,CAACX,SAAS,IAAI,CAAC,IAAI,CAACD,SAAS,EAAE;QACtCa,UAAU,CAAC,IAAI,CAACC,MAAM,CAACC,IAAI,CAAC,IAAI,EAAE3B,OAAO,CAAC4B,OAAO,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;MAC7D;IACF,CAAC,CAAC;IACF,IAAI,CAAClB,OAAO,CAACmB,EAAE,CAAC9B,OAAO,CAAC+B,MAAM,CAACC,oBAAoB,EAAE,MAAM;MACzD,IAAI,CAAC,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE;MACtB,MAAMC,MAAM,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACpC,IAAID,MAAM,IAAI,IAAI,EAAE;MACpB,IAAIA,MAAM,CAACE,KAAK,CAACC,IAAI,KAAK,IAAI,CAACrB,MAAM,CAACsB,QAAQ,EAAE,OAAO,CAAC;MACxD,IAAI,CAAC3B,OAAO,CAAC4B,IAAI,CACfvC,OAAO,CAAC+B,MAAM,CAACS,aAAa,EAC5B,CAACC,MAAM,EAAEC,SAA2B,KAAK;QACvC,IAAI;UACF,IACE,IAAI,CAAC5B,IAAI,CAAC6B,QAAQ,CAACT,MAAM,CAACE,KAAK,CAACC,IAAI,CAAC,IACrC,IAAI,CAACvB,IAAI,CAAC6B,QAAQ,CAACT,MAAM,CAACU,GAAG,CAACP,IAAI,CAAC,EACnC;YACA,IAAI,CAACQ,cAAc,CACjBX,MAAM,CAACE,KAAK,CAACC,IAAI,EACjBH,MAAM,CAACE,KAAK,CAACU,MAAM,EACnBZ,MAAM,CAACU,GAAG,CAACP,IAAI,EACfH,MAAM,CAACU,GAAG,CAACE,MACb,CAAC;UACH;UACA,MAAMC,iBAAiB,GAAGL,SAAS,CAACM,IAAI,CACrCC,QAAQ,IACPA,QAAQ,CAACC,IAAI,KAAK,eAAe,IACjCD,QAAQ,CAACC,IAAI,KAAK,WAAW,IAC5BD,QAAQ,CAACC,IAAI,KAAK,YAAY,IAC7BD,QAAQ,CAACE,MAAM,KAAK,IAAI,CAACrC,IAC/B,CAAC;UACD,IAAI,CAACY,MAAM,CAACqB,iBAAiB,GAAG/C,OAAO,CAAC4B,OAAO,CAACwB,MAAM,GAAGX,MAAM,CAAC;QAClE,CAAC,CAAC,OAAOY,OAAO,EAAE;UAChB;QAAA;MAEJ,CACF,CAAC;IACH,CAAC,CAAC;IACF,IAAI,CAAC1C,OAAO,CAACmB,EAAE,CAAC9B,OAAO,CAAC+B,MAAM,CAACuB,eAAe,EAAE,CAACZ,SAAS,EAAEa,OAAO,KAAK;MACtE,IAAIA,OAAO,CAACC,KAAK,EAAE;QACjB,MAAM;UAAEC,SAAS;UAAEC,WAAW;UAAEC,OAAO;UAAEC;QAAU,CAAC,GAAGL,OAAO,CAACC,KAAK;QACpE,IAAI,CAACX,cAAc,CAACY,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,SAAS,CAAC;QAC/D,IAAI,CAAClC,MAAM,CAAC1B,OAAO,CAAC4B,OAAO,CAACwB,MAAM,CAAC;MACrC;IACF,CAAC,CAAC;IACF,IAAI,CAAC1B,MAAM,CAAC1B,OAAO,CAAC4B,OAAO,CAACwB,MAAM,CAAC;EACrC;EAEA/B,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACV,OAAO,CAACmB,EAAE,CAAC9B,OAAO,CAAC+B,MAAM,CAAC8B,wBAAwB,EAAE,MAAM;MAC7D,IAAI,CAACjD,SAAS,GAAG,IAAI;IACvB,CAAC,CAAC;IACF,IAAI,CAACD,OAAO,CAACmB,EAAE,CAAC9B,OAAO,CAAC+B,MAAM,CAAC+B,eAAe,EAAE,MAAM;MACpD,IAAI,CAAClD,SAAS,GAAG,KAAK;MACtB,IAAI,IAAI,CAACI,MAAM,CAAC+C,MAAM,EAAE;QACtB,MAAMP,KAAK,GAAG,IAAI,CAACxC,MAAM,CAACgD,OAAO,CAAC,CAAC;QACnC,IAAI,CAACR,KAAK,EAAE;QACZ/B,UAAU,CAAC,MAAM;UACf,IAAI,CAACoB,cAAc,CACjBW,KAAK,CAACC,SAAS,EACfD,KAAK,CAACE,WAAW,EACjBF,KAAK,CAACG,OAAO,EACbH,KAAK,CAACI,SACR,CAAC;QACH,CAAC,EAAE,CAAC,CAAC;MACP;IACF,CAAC,CAAC;EACJ;EAEAtC,cAAcA,CAAA,EAAG;IACf,IAAI,CAACX,OAAO,CAACY,SAAS,CAAC,WAAW,EAAEC,QAAQ,CAACyC,IAAI,EAAE,MAAM;MACvD,IAAI,CAACpD,SAAS,GAAG,IAAI;IACvB,CAAC,CAAC;IACF,IAAI,CAACF,OAAO,CAACY,SAAS,CAAC,SAAS,EAAEC,QAAQ,CAACyC,IAAI,EAAE,MAAM;MACrD,IAAI,CAACpD,SAAS,GAAG,KAAK;MACtB,IAAI,CAACa,MAAM,CAAC1B,OAAO,CAAC4B,OAAO,CAACC,IAAI,CAAC;IACnC,CAAC,CAAC;EACJ;EAEAqC,KAAKA,CAAA,EAAG;IACN,IAAI,IAAI,CAACjC,QAAQ,CAAC,CAAC,EAAE;IACrB,IAAI,CAACnB,IAAI,CAACoD,KAAK,CAAC;MAAEC,aAAa,EAAE;IAAK,CAAC,CAAC;IACxC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAClD,UAAU,CAAC;EAChC;EAEAmD,MAAMA,CAACA,MAAc,EAAEC,KAAc,EAAE;IACrC,IAAI,CAAC5D,MAAM,CAACgB,MAAM,CAAC,CAAC;IACpB,MAAM6C,WAAW,GAAG,IAAI,CAACpC,cAAc,CAAC,CAAC;IACzC,IACEoC,WAAW,IAAI,IAAI,IACnB,CAACA,WAAW,CAACrC,MAAM,CAACsC,SAAS,IAC7B,IAAI,CAAC9D,MAAM,CAAC+D,KAAK,CAACJ,MAAM,EAAExE,KAAK,CAAC6E,KAAK,CAAC,EAEtC;IACF,IAAIH,WAAW,CAACnC,KAAK,CAACC,IAAI,KAAK,IAAI,CAACrB,MAAM,CAACsB,QAAQ,EAAE;MACnD,MAAMqC,IAAI,GAAG,IAAI,CAACjE,MAAM,CAACkE,IAAI,CAACL,WAAW,CAACnC,KAAK,CAACC,IAAI,EAAE,KAAK,CAAC;MAC5D,IAAIsC,IAAI,IAAI,IAAI,EAAE;MAClB;MACA,IAAIA,IAAI,YAAY/E,QAAQ,EAAE;QAC5B,MAAMiF,KAAK,GAAGF,IAAI,CAACG,KAAK,CAACP,WAAW,CAACnC,KAAK,CAACU,MAAM,CAAC;QAClD6B,IAAI,CAACZ,MAAM,CAACgB,YAAY,CAAC,IAAI,CAAC/D,MAAM,EAAE6D,KAAK,CAAC;MAC9C,CAAC,MAAM;QACL;QACAF,IAAI,CAACI,YAAY,CAAC,IAAI,CAAC/D,MAAM,EAAEuD,WAAW,CAACnC,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;MAC1D;MACA,IAAI,CAACrB,MAAM,CAACgE,MAAM,CAAC,CAAC;IACtB;IACA,IAAI,CAAChE,MAAM,CAACqD,MAAM,CAACA,MAAM,EAAEC,KAAK,CAAC;IACjC,IAAI,CAAC5D,MAAM,CAACuE,QAAQ,CAAC,CAAC;IACtB,IAAI,CAACpC,cAAc,CAAC,IAAI,CAAC7B,MAAM,CAACsB,QAAQ,EAAE,IAAI,CAACtB,MAAM,CAACsB,QAAQ,CAAC4C,IAAI,CAAC5E,MAAM,CAAC;IAC3E,IAAI,CAACoB,MAAM,CAAC,CAAC;EACf;EAEAyD,SAASA,CAAC9E,KAAa,EAAc;IAAA,IAAZC,MAAM,GAAAC,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC;IACjC,MAAM6E,YAAY,GAAG,IAAI,CAAC1E,MAAM,CAACJ,MAAM,CAAC,CAAC;IACzCD,KAAK,GAAGgF,IAAI,CAACC,GAAG,CAACjF,KAAK,EAAE+E,YAAY,GAAG,CAAC,CAAC;IACzC9E,MAAM,GAAG+E,IAAI,CAACC,GAAG,CAACjF,KAAK,GAAGC,MAAM,EAAE8E,YAAY,GAAG,CAAC,CAAC,GAAG/E,KAAK;IAC3D,IAAIgC,IAAU;IACd,IAAI,CAACkD,IAAI,EAAEzC,MAAM,CAAC,GAAG,IAAI,CAACpC,MAAM,CAAC6E,IAAI,CAAClF,KAAK,CAAC;IAC5C,IAAIkF,IAAI,IAAI,IAAI,EAAE,OAAO,IAAI;IAC7B,IAAIjF,MAAM,GAAG,CAAC,IAAIwC,MAAM,KAAKyC,IAAI,CAACjF,MAAM,CAAC,CAAC,EAAE;MAC1C,MAAM,CAACkF,IAAI,CAAC,GAAG,IAAI,CAAC9E,MAAM,CAAC6E,IAAI,CAAClF,KAAK,GAAG,CAAC,CAAC;MAC1C,IAAImF,IAAI,EAAE;QACR,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAAC/E,MAAM,CAAC+E,IAAI,CAACpF,KAAK,CAAC;QACtC,MAAM,CAACqF,QAAQ,CAAC,GAAG,IAAI,CAAChF,MAAM,CAAC+E,IAAI,CAACpF,KAAK,GAAG,CAAC,CAAC;QAC9C,IAAIoF,IAAI,KAAKC,QAAQ,EAAE;UACrBH,IAAI,GAAGC,IAAI;UACX1C,MAAM,GAAG,CAAC;QACZ;MACF;IACF;IACA,CAACT,IAAI,EAAES,MAAM,CAAC,GAAGyC,IAAI,CAACI,QAAQ,CAAC7C,MAAM,EAAE,IAAI,CAAC;IAC5C,MAAMU,KAAK,GAAGhC,QAAQ,CAACoE,WAAW,CAAC,CAAC;IACpC,IAAItF,MAAM,GAAG,CAAC,EAAE;MACdkD,KAAK,CAACqC,QAAQ,CAACxD,IAAI,EAAES,MAAM,CAAC;MAC5B,CAACyC,IAAI,EAAEzC,MAAM,CAAC,GAAG,IAAI,CAACpC,MAAM,CAAC6E,IAAI,CAAClF,KAAK,GAAGC,MAAM,CAAC;MACjD,IAAIiF,IAAI,IAAI,IAAI,EAAE,OAAO,IAAI;MAC7B,CAAClD,IAAI,EAAES,MAAM,CAAC,GAAGyC,IAAI,CAACI,QAAQ,CAAC7C,MAAM,EAAE,IAAI,CAAC;MAC5CU,KAAK,CAACsC,MAAM,CAACzD,IAAI,EAAES,MAAM,CAAC;MAC1B,OAAOU,KAAK,CAACuC,qBAAqB,CAAC,CAAC;IACtC;IACA,IAAIC,IAAsB,GAAG,MAAM;IACnC,IAAIC,IAAa;IACjB,IAAI5D,IAAI,YAAY6D,IAAI,EAAE;MACxB;MACA;MACA;MACA;MACA;MACA,IAAI,CAAC7D,IAAI,CAAC6C,IAAI,CAAC5E,MAAM,EAAE;QACrB,OAAO,IAAI;MACb;MACA,IAAIwC,MAAM,GAAGT,IAAI,CAAC6C,IAAI,CAAC5E,MAAM,EAAE;QAC7BkD,KAAK,CAACqC,QAAQ,CAACxD,IAAI,EAAES,MAAM,CAAC;QAC5BU,KAAK,CAACsC,MAAM,CAACzD,IAAI,EAAES,MAAM,GAAG,CAAC,CAAC;MAChC,CAAC,MAAM;QACLU,KAAK,CAACqC,QAAQ,CAACxD,IAAI,EAAES,MAAM,GAAG,CAAC,CAAC;QAChCU,KAAK,CAACsC,MAAM,CAACzD,IAAI,EAAES,MAAM,CAAC;QAC1BkD,IAAI,GAAG,OAAO;MAChB;MACAC,IAAI,GAAGzC,KAAK,CAACuC,qBAAqB,CAAC,CAAC;IACtC,CAAC,MAAM;MACL,IAAI,EAAER,IAAI,CAACxE,OAAO,YAAYoF,OAAO,CAAC,EAAE,OAAO,IAAI;MACnDF,IAAI,GAAGV,IAAI,CAACxE,OAAO,CAACgF,qBAAqB,CAAC,CAAC;MAC3C,IAAIjD,MAAM,GAAG,CAAC,EAAEkD,IAAI,GAAG,OAAO;IAChC;IACA,OAAO;MACLI,MAAM,EAAEH,IAAI,CAACI,GAAG,GAAGJ,IAAI,CAACK,MAAM;MAC9BA,MAAM,EAAEL,IAAI,CAACK,MAAM;MACnBC,IAAI,EAAEN,IAAI,CAACD,IAAI,CAAC;MAChBQ,KAAK,EAAEP,IAAI,CAACD,IAAI,CAAC;MACjBK,GAAG,EAAEJ,IAAI,CAACI,GAAG;MACbI,KAAK,EAAE;IACT,CAAC;EACH;EAEAtE,cAAcA,CAAA,EAA2B;IACvC,MAAMuE,SAAS,GAAGlF,QAAQ,CAACmF,YAAY,CAAC,CAAC;IACzC,IAAID,SAAS,IAAI,IAAI,IAAIA,SAAS,CAACE,UAAU,IAAI,CAAC,EAAE,OAAO,IAAI;IAC/D,MAAMrC,WAAW,GAAGmC,SAAS,CAACG,UAAU,CAAC,CAAC,CAAC;IAC3C,IAAItC,WAAW,IAAI,IAAI,EAAE,OAAO,IAAI;IACpC,MAAMf,KAAK,GAAG,IAAI,CAACsD,eAAe,CAACvC,WAAW,CAAC;IAC/CrE,KAAK,CAAC6G,IAAI,CAAC,gBAAgB,EAAEvD,KAAK,CAAC;IACnC,OAAOA,KAAK;EACd;EAEAwD,QAAQA,CAAA,EAA4C;IAClD,MAAMlG,IAAI,GAAG,IAAI,CAACJ,MAAM,CAACK,OAAO;IAChC,IAAI,aAAa,IAAID,IAAI,IAAI,CAACA,IAAI,CAACmG,WAAW,EAAE;MAC9C;MACA;MACA,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;IACrB;IACA,MAAMC,UAAU,GAAG,IAAI,CAAC/E,cAAc,CAAC,CAAC;IACxC,IAAI+E,UAAU,IAAI,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;IAC3C,MAAM1D,KAAK,GAAG,IAAI,CAAC2D,iBAAiB,CAACD,UAAU,CAAC;IAChD,OAAO,CAAC1D,KAAK,EAAE0D,UAAU,CAAC;EAC5B;EAEAjF,QAAQA,CAAA,EAAY;IAClB,OACET,QAAQ,CAAC4F,aAAa,KAAK,IAAI,CAACtG,IAAI,IACnCU,QAAQ,CAAC4F,aAAa,IAAI,IAAI,IAC7BzE,QAAQ,CAAC,IAAI,CAAC7B,IAAI,EAAEU,QAAQ,CAAC4F,aAAa,CAAE;EAElD;EAEAD,iBAAiBA,CAAC3D,KAAsB,EAAE;IACxC,MAAM6D,SAA2B,GAAG,CAClC,CAAC7D,KAAK,CAACpB,KAAK,CAACC,IAAI,EAAEmB,KAAK,CAACpB,KAAK,CAACU,MAAM,CAAC,CACvC;IACD,IAAI,CAACU,KAAK,CAACtB,MAAM,CAACsC,SAAS,EAAE;MAC3B6C,SAAS,CAACC,IAAI,CAAC,CAAC9D,KAAK,CAACZ,GAAG,CAACP,IAAI,EAAEmB,KAAK,CAACZ,GAAG,CAACE,MAAM,CAAC,CAAC;IACpD;IACA,MAAMyE,OAAO,GAAGF,SAAS,CAACG,GAAG,CAAE7B,QAAQ,IAAK;MAC1C,MAAM,CAACtD,IAAI,EAAES,MAAM,CAAC,GAAG6C,QAAQ;MAC/B,MAAMhB,IAAI,GAAG,IAAI,CAACjE,MAAM,CAACkE,IAAI,CAACvC,IAAI,EAAE,IAAI,CAAC;MACzC;MACA,MAAMhC,KAAK,GAAGsE,IAAI,CAAC7B,MAAM,CAAC,IAAI,CAACpC,MAAM,CAAC;MACtC,IAAIoC,MAAM,KAAK,CAAC,EAAE;QAChB,OAAOzC,KAAK;MACd;MACA,IAAIsE,IAAI,YAAY/E,QAAQ,EAAE;QAC5B,OAAOS,KAAK,GAAGsE,IAAI,CAACtE,KAAK,CAACgC,IAAI,EAAES,MAAM,CAAC;MACzC;MACA;MACA,OAAOzC,KAAK,GAAGsE,IAAI,CAACrE,MAAM,CAAC,CAAC;IAC9B,CAAC,CAAC;IACF,MAAMsC,GAAG,GAAGyC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACoC,GAAG,CAAC,GAAGF,OAAO,CAAC,EAAE,IAAI,CAAC7G,MAAM,CAACJ,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;IACpE,MAAM8B,KAAK,GAAGiD,IAAI,CAACC,GAAG,CAAC1C,GAAG,EAAE,GAAG2E,OAAO,CAAC;IACvC,OAAO,IAAIpH,KAAK,CAACiC,KAAK,EAAEQ,GAAG,GAAGR,KAAK,CAAC;EACtC;EAEA0E,eAAeA,CAACvC,WAAwB,EAAE;IACxC,IACE,CAAC5B,QAAQ,CAAC,IAAI,CAAC7B,IAAI,EAAEyD,WAAW,CAACmD,cAAc,CAAC,IAC/C,CAACnD,WAAW,CAACC,SAAS,IAAI,CAAC7B,QAAQ,CAAC,IAAI,CAAC7B,IAAI,EAAEyD,WAAW,CAACoD,YAAY,CAAE,EAC1E;MACA,OAAO,IAAI;IACb;IACA,MAAMnE,KAAK,GAAG;MACZpB,KAAK,EAAE;QACLC,IAAI,EAAEkC,WAAW,CAACmD,cAAc;QAChC5E,MAAM,EAAEyB,WAAW,CAACb;MACtB,CAAC;MACDd,GAAG,EAAE;QAAEP,IAAI,EAAEkC,WAAW,CAACoD,YAAY;QAAE7E,MAAM,EAAEyB,WAAW,CAACX;MAAU,CAAC;MACtE1B,MAAM,EAAEqC;IACV,CAAC;IACD,CAACf,KAAK,CAACpB,KAAK,EAAEoB,KAAK,CAACZ,GAAG,CAAC,CAACgF,OAAO,CAAEjC,QAAQ,IAAK;MAC7C,IAAI;QAAEtD,IAAI;QAAES;MAAO,CAAC,GAAG6C,QAAQ;MAC/B,OAAO,EAAEtD,IAAI,YAAY6D,IAAI,CAAC,IAAI7D,IAAI,CAACwF,UAAU,CAACvH,MAAM,GAAG,CAAC,EAAE;QAC5D,IAAI+B,IAAI,CAACwF,UAAU,CAACvH,MAAM,GAAGwC,MAAM,EAAE;UACnCT,IAAI,GAAGA,IAAI,CAACwF,UAAU,CAAC/E,MAAM,CAAC;UAC9BA,MAAM,GAAG,CAAC;QACZ,CAAC,MAAM,IAAIT,IAAI,CAACwF,UAAU,CAACvH,MAAM,KAAKwC,MAAM,EAAE;UAC5C;UACAT,IAAI,GAAGA,IAAI,CAACyF,SAAS;UACrB,IAAIzF,IAAI,YAAY6D,IAAI,EAAE;YACxBpD,MAAM,GAAGT,IAAI,CAAC6C,IAAI,CAAC5E,MAAM;UAC3B,CAAC,MAAM,IAAI+B,IAAI,CAACwF,UAAU,CAACvH,MAAM,GAAG,CAAC,EAAE;YACrC;YACAwC,MAAM,GAAGT,IAAI,CAACwF,UAAU,CAACvH,MAAM;UACjC,CAAC,MAAM;YACL;YACAwC,MAAM,GAAGT,IAAI,CAACwF,UAAU,CAACvH,MAAM,GAAG,CAAC;UACrC;QACF,CAAC,MAAM;UACL;QACF;MACF;MACAqF,QAAQ,CAACtD,IAAI,GAAGA,IAAI;MACpBsD,QAAQ,CAAC7C,MAAM,GAAGA,MAAM;IAC1B,CAAC,CAAC;IACF,OAAOU,KAAK;EACd;EAEAuE,aAAaA,CAACvE,KAAY,EAA8C;IACtE,MAAM4B,YAAY,GAAG,IAAI,CAAC1E,MAAM,CAACJ,MAAM,CAAC,CAAC;IAEzC,MAAM0H,WAAW,GAAGA,CAClB3H,KAAa,EACb4H,SAAkB,KACQ;MAC1B5H,KAAK,GAAGgF,IAAI,CAACC,GAAG,CAACF,YAAY,GAAG,CAAC,EAAE/E,KAAK,CAAC;MACzC,MAAM,CAACkF,IAAI,EAAE2C,UAAU,CAAC,GAAG,IAAI,CAACxH,MAAM,CAAC6E,IAAI,CAAClF,KAAK,CAAC;MAClD,OAAOkF,IAAI,GAAGA,IAAI,CAACI,QAAQ,CAACuC,UAAU,EAAED,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IACjE,CAAC;IACD,OAAO,CACL,GAAGD,WAAW,CAACxE,KAAK,CAACnD,KAAK,EAAE,KAAK,CAAC,EAClC,GAAG2H,WAAW,CAACxE,KAAK,CAACnD,KAAK,GAAGmD,KAAK,CAAClD,MAAM,EAAE,IAAI,CAAC,CACjD;EACH;EAEAuC,cAAcA,CACZY,SAAsB,EACtBC,WAAoB,EAIpB;IAAA,IAHAC,OAAO,GAAApD,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGkD,SAAS;IAAA,IACnBG,SAAS,GAAArD,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGmD,WAAW;IAAA,IACvByE,KAAK,GAAA5H,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;IAEbL,KAAK,CAAC6G,IAAI,CAAC,gBAAgB,EAAEtD,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,SAAS,CAAC;IACxE,IACEH,SAAS,IAAI,IAAI,KAChB,IAAI,CAAC3C,IAAI,CAACsH,UAAU,IAAI,IAAI,IAC3B3E,SAAS,CAAC2E,UAAU,IAAI,IAAI;IAC5B;IACAzE,OAAO,CAACyE,UAAU,IAAI,IAAI,CAAC,EAC7B;MACA;IACF;IACA,MAAM1B,SAAS,GAAGlF,QAAQ,CAACmF,YAAY,CAAC,CAAC;IACzC,IAAID,SAAS,IAAI,IAAI,EAAE;IACvB,IAAIjD,SAAS,IAAI,IAAI,EAAE;MACrB,IAAI,CAAC,IAAI,CAACxB,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACnB,IAAI,CAACoD,KAAK,CAAC;QAAEC,aAAa,EAAE;MAAK,CAAC,CAAC;MAC9D,MAAM;QAAEjC;MAAO,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC;MAC9C,IACED,MAAM,IAAI,IAAI,IACdiG,KAAK,IACL1E,SAAS,KAAKvB,MAAM,CAACwF,cAAc,IACnChE,WAAW,KAAKxB,MAAM,CAACwB,WAAW,IAClCC,OAAO,KAAKzB,MAAM,CAACyF,YAAY,IAC/B/D,SAAS,KAAK1B,MAAM,CAAC0B,SAAS,EAC9B;QACA,IAAIH,SAAS,YAAY0C,OAAO,IAAI1C,SAAS,CAAC4E,OAAO,KAAK,IAAI,EAAE;UAC9D;UACA3E,WAAW,GAAG4E,KAAK,CAACC,IAAI,CAAC9E,SAAS,CAAC2E,UAAU,CAACP,UAAU,CAAC,CAACW,OAAO,CAC/D/E,SACF,CAAC;UACDA,SAAS,GAAGA,SAAS,CAAC2E,UAAU;QAClC;QACA,IAAIzE,OAAO,YAAYwC,OAAO,IAAIxC,OAAO,CAAC0E,OAAO,KAAK,IAAI,EAAE;UAC1D;UACAzE,SAAS,GAAG0E,KAAK,CAACC,IAAI,CAAC5E,OAAO,CAACyE,UAAU,CAACP,UAAU,CAAC,CAACW,OAAO,CAC3D7E,OACF,CAAC;UACDA,OAAO,GAAGA,OAAO,CAACyE,UAAU;QAC9B;QACA,MAAM5E,KAAK,GAAGhC,QAAQ,CAACoE,WAAW,CAAC,CAAC;QACpC;QACApC,KAAK,CAACqC,QAAQ,CAACpC,SAAS,EAAEC,WAAW,CAAC;QACtC;QACAF,KAAK,CAACsC,MAAM,CAACnC,OAAO,EAAEC,SAAS,CAAC;QAChC8C,SAAS,CAAC+B,eAAe,CAAC,CAAC;QAC3B/B,SAAS,CAACgC,QAAQ,CAAClF,KAAK,CAAC;MAC3B;IACF,CAAC,MAAM;MACLkD,SAAS,CAAC+B,eAAe,CAAC,CAAC;MAC3B,IAAI,CAAC3H,IAAI,CAAC6H,IAAI,CAAC,CAAC;IAClB;EACF;EAIAvE,QAAQA,CACNZ,KAAmB,EAGb;IAAA,IAFN2E,KAA8B,GAAA5H,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;IAAA,IACtCkC,MAAqB,GAAAlC,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGP,OAAO,CAAC4B,OAAO,CAACgH,GAAG;IAE3C,IAAI,OAAOT,KAAK,KAAK,QAAQ,EAAE;MAC7B1F,MAAM,GAAG0F,KAAK;MACdA,KAAK,GAAG,KAAK;IACf;IACAjI,KAAK,CAAC6G,IAAI,CAAC,UAAU,EAAEvD,KAAK,CAAC;IAC7B,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,MAAMqF,IAAI,GAAG,IAAI,CAACd,aAAa,CAACvE,KAAK,CAAC;MACtC,IAAI,CAACX,cAAc,CAAC,GAAGgG,IAAI,EAAEV,KAAK,CAAC;IACrC,CAAC,MAAM;MACL,IAAI,CAACtF,cAAc,CAAC,IAAI,CAAC;IAC3B;IACA,IAAI,CAACnB,MAAM,CAACe,MAAM,CAAC;EACrB;EAEAf,MAAMA,CAAA,EAA+C;IAAA,IAA9Ce,MAAqB,GAAAlC,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGP,OAAO,CAAC4B,OAAO,CAACC,IAAI;IACjD,MAAMiH,QAAQ,GAAG,IAAI,CAAC3H,SAAS;IAC/B,MAAM,CAACA,SAAS,EAAEoD,WAAW,CAAC,GAAG,IAAI,CAACyC,QAAQ,CAAC,CAAC;IAChD,IAAI,CAAC7F,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,UAAU,GAAGmD,WAAW;IAC7B,IAAI,IAAI,CAACpD,SAAS,IAAI,IAAI,EAAE;MAC1B,IAAI,CAACD,UAAU,GAAG,IAAI,CAACC,SAAS;IAClC;IACA,IAAI,CAACpB,OAAO,CAAC+I,QAAQ,EAAE,IAAI,CAAC3H,SAAS,CAAC,EAAE;MACtC,IACE,CAAC,IAAI,CAACP,SAAS,IACf2D,WAAW,IAAI,IAAI,IACnBA,WAAW,CAACrC,MAAM,CAACsC,SAAS,IAC5BD,WAAW,CAACnC,KAAK,CAACC,IAAI,KAAK,IAAI,CAACrB,MAAM,CAACsB,QAAQ,EAC/C;QACA,MAAMkB,KAAK,GAAG,IAAI,CAACxC,MAAM,CAACgD,OAAO,CAAC,CAAC;QACnC,IAAIR,KAAK,EAAE;UACT,IAAI,CAACX,cAAc,CACjBW,KAAK,CAACC,SAAS,EACfD,KAAK,CAACE,WAAW,EACjBF,KAAK,CAACG,OAAO,EACbH,KAAK,CAACI,SACR,CAAC;QACH;MACF;MACA,MAAMiF,IAAI,GAAG,CACX7I,OAAO,CAAC+B,MAAM,CAACgH,gBAAgB,EAC/BjJ,SAAS,CAAC,IAAI,CAACqB,SAAS,CAAC,EACzBrB,SAAS,CAACgJ,QAAQ,CAAC,EACnBrG,MAAM,CACP;MACD,IAAI,CAAC9B,OAAO,CAACqI,IAAI,CAAChJ,OAAO,CAAC+B,MAAM,CAACkH,aAAa,EAAE,GAAGJ,IAAI,CAAC;MACxD,IAAIpG,MAAM,KAAKzC,OAAO,CAAC4B,OAAO,CAACwB,MAAM,EAAE;QACrC,IAAI,CAACzC,OAAO,CAACqI,IAAI,CAAC,GAAGH,IAAI,CAAC;MAC5B;IACF;EACF;AACF;AAEA,SAASlG,QAAQA,CAACoB,MAAY,EAAEmF,UAAgB,EAAE;EAChD,IAAI;IACF;IACAA,UAAU,CAACd,UAAU,CAAC,CAAC;EACzB,CAAC,CAAC,OAAOe,CAAC,EAAE;IACV,OAAO,KAAK;EACd;EACA,OAAOpF,MAAM,CAACpB,QAAQ,CAACuG,UAAU,CAAC;AACpC;AAEA,eAAezI,SAAS", "ignoreList": []}