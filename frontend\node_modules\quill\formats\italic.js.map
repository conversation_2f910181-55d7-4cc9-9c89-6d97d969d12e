{"version": 3, "file": "italic.js", "names": ["Bold", "Italic", "blotName", "tagName"], "sources": ["../../src/formats/italic.ts"], "sourcesContent": ["import Bold from './bold.js';\n\nclass Italic extends Bold {\n  static blotName = 'italic';\n  static tagName = ['EM', 'I'];\n}\n\nexport default Italic;\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,WAAW;AAE5B,MAAMC,MAAM,SAASD,IAAI,CAAC;EACxB,OAAOE,QAAQ,GAAG,QAAQ;EAC1B,OAAOC,OAAO,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;AAC9B;AAEA,eAAeF,MAAM", "ignoreList": []}