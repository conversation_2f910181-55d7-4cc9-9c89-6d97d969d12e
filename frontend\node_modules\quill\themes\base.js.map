{"version": 3, "file": "base.js", "names": ["merge", "Emitter", "Theme", "ColorPicker", "IconPicker", "Picker", "<PERSON><PERSON><PERSON>", "ALIGNS", "COLORS", "FONTS", "HEADERS", "SIZES", "BaseTheme", "constructor", "quill", "options", "listener", "e", "document", "body", "contains", "root", "removeEventListener", "tooltip", "target", "activeElement", "textbox", "hasFocus", "hide", "pickers", "for<PERSON>ach", "picker", "container", "close", "emitter", "listenDOM", "addModule", "name", "module", "extendToolbar", "buildButtons", "buttons", "icons", "Array", "from", "button", "className", "getAttribute", "split", "startsWith", "slice", "length", "innerHTML", "rtl", "value", "buildPickers", "selects", "map", "select", "classList", "querySelector", "fillSelect", "align", "format", "update", "on", "events", "EDITOR_CHANGE", "DEFAULTS", "modules", "toolbar", "handlers", "formula", "theme", "edit", "image", "fileInput", "createElement", "setAttribute", "uploader", "mimetypes", "join", "add", "addEventListener", "range", "getSelection", "upload", "files", "append<PERSON><PERSON><PERSON>", "click", "video", "BaseTooltip", "boundsContainer", "listen", "event", "key", "save", "preventDefault", "cancel", "restoreFocus", "mode", "arguments", "undefined", "preview", "remove", "bounds", "getBounds", "selection", "savedRange", "position", "focus", "preventScroll", "scrollTop", "linkRange", "formatText", "sources", "USER", "extractVideoUrl", "index", "insertEmbed", "insertText", "setSelection", "url", "match", "values", "defaultValue", "option", "String", "default"], "sources": ["../../src/themes/base.ts"], "sourcesContent": ["import { merge } from 'lodash-es';\nimport type Quill from '../core/quill.js';\nimport Emitter from '../core/emitter.js';\nimport Theme from '../core/theme.js';\nimport type { ThemeOptions } from '../core/theme.js';\nimport ColorPicker from '../ui/color-picker.js';\nimport IconPicker from '../ui/icon-picker.js';\nimport Picker from '../ui/picker.js';\nimport Tooltip from '../ui/tooltip.js';\nimport type { Range } from '../core/selection.js';\nimport type Clipboard from '../modules/clipboard.js';\nimport type History from '../modules/history.js';\nimport type Keyboard from '../modules/keyboard.js';\nimport type Uploader from '../modules/uploader.js';\nimport type Selection from '../core/selection.js';\n\nconst ALIGNS = [false, 'center', 'right', 'justify'];\n\nconst COLORS = [\n  '#000000',\n  '#e60000',\n  '#ff9900',\n  '#ffff00',\n  '#008a00',\n  '#0066cc',\n  '#9933ff',\n  '#ffffff',\n  '#facccc',\n  '#ffebcc',\n  '#ffffcc',\n  '#cce8cc',\n  '#cce0f5',\n  '#ebd6ff',\n  '#bbbbbb',\n  '#f06666',\n  '#ffc266',\n  '#ffff66',\n  '#66b966',\n  '#66a3e0',\n  '#c285ff',\n  '#888888',\n  '#a10000',\n  '#b26b00',\n  '#b2b200',\n  '#006100',\n  '#0047b2',\n  '#6b24b2',\n  '#444444',\n  '#5c0000',\n  '#663d00',\n  '#666600',\n  '#003700',\n  '#002966',\n  '#3d1466',\n];\n\nconst FONTS = [false, 'serif', 'monospace'];\n\nconst HEADERS = ['1', '2', '3', false];\n\nconst SIZES = ['small', false, 'large', 'huge'];\n\nclass BaseTheme extends Theme {\n  pickers: Picker[];\n  tooltip?: Tooltip;\n\n  constructor(quill: Quill, options: ThemeOptions) {\n    super(quill, options);\n    const listener = (e: MouseEvent) => {\n      if (!document.body.contains(quill.root)) {\n        document.body.removeEventListener('click', listener);\n        return;\n      }\n      if (\n        this.tooltip != null &&\n        // @ts-expect-error\n        !this.tooltip.root.contains(e.target) &&\n        // @ts-expect-error\n        document.activeElement !== this.tooltip.textbox &&\n        !this.quill.hasFocus()\n      ) {\n        this.tooltip.hide();\n      }\n      if (this.pickers != null) {\n        this.pickers.forEach((picker) => {\n          // @ts-expect-error\n          if (!picker.container.contains(e.target)) {\n            picker.close();\n          }\n        });\n      }\n    };\n    quill.emitter.listenDOM('click', document.body, listener);\n  }\n\n  addModule(name: 'clipboard'): Clipboard;\n  addModule(name: 'keyboard'): Keyboard;\n  addModule(name: 'uploader'): Uploader;\n  addModule(name: 'history'): History;\n  addModule(name: 'selection'): Selection;\n  addModule(name: string): unknown;\n  addModule(name: string) {\n    const module = super.addModule(name);\n    if (name === 'toolbar') {\n      // @ts-expect-error\n      this.extendToolbar(module);\n    }\n    return module;\n  }\n\n  buildButtons(\n    buttons: NodeListOf<HTMLElement>,\n    icons: Record<string, Record<string, string> | string>,\n  ) {\n    Array.from(buttons).forEach((button) => {\n      const className = button.getAttribute('class') || '';\n      className.split(/\\s+/).forEach((name) => {\n        if (!name.startsWith('ql-')) return;\n        name = name.slice('ql-'.length);\n        if (icons[name] == null) return;\n        if (name === 'direction') {\n          // @ts-expect-error\n          button.innerHTML = icons[name][''] + icons[name].rtl;\n        } else if (typeof icons[name] === 'string') {\n          // @ts-expect-error\n          button.innerHTML = icons[name];\n        } else {\n          // @ts-expect-error\n          const value = button.value || '';\n          // @ts-expect-error\n          if (value != null && icons[name][value]) {\n            // @ts-expect-error\n            button.innerHTML = icons[name][value];\n          }\n        }\n      });\n    });\n  }\n\n  buildPickers(\n    selects: NodeListOf<HTMLSelectElement>,\n    icons: Record<string, string | Record<string, string>>,\n  ) {\n    this.pickers = Array.from(selects).map((select) => {\n      if (select.classList.contains('ql-align')) {\n        if (select.querySelector('option') == null) {\n          fillSelect(select, ALIGNS);\n        }\n        if (typeof icons.align === 'object') {\n          return new IconPicker(select, icons.align);\n        }\n      }\n      if (\n        select.classList.contains('ql-background') ||\n        select.classList.contains('ql-color')\n      ) {\n        const format = select.classList.contains('ql-background')\n          ? 'background'\n          : 'color';\n        if (select.querySelector('option') == null) {\n          fillSelect(\n            select,\n            COLORS,\n            format === 'background' ? '#ffffff' : '#000000',\n          );\n        }\n        return new ColorPicker(select, icons[format] as string);\n      }\n      if (select.querySelector('option') == null) {\n        if (select.classList.contains('ql-font')) {\n          fillSelect(select, FONTS);\n        } else if (select.classList.contains('ql-header')) {\n          fillSelect(select, HEADERS);\n        } else if (select.classList.contains('ql-size')) {\n          fillSelect(select, SIZES);\n        }\n      }\n      return new Picker(select);\n    });\n    const update = () => {\n      this.pickers.forEach((picker) => {\n        picker.update();\n      });\n    };\n    this.quill.on(Emitter.events.EDITOR_CHANGE, update);\n  }\n}\nBaseTheme.DEFAULTS = merge({}, Theme.DEFAULTS, {\n  modules: {\n    toolbar: {\n      handlers: {\n        formula() {\n          this.quill.theme.tooltip.edit('formula');\n        },\n        image() {\n          let fileInput = this.container.querySelector(\n            'input.ql-image[type=file]',\n          );\n          if (fileInput == null) {\n            fileInput = document.createElement('input');\n            fileInput.setAttribute('type', 'file');\n            fileInput.setAttribute(\n              'accept',\n              this.quill.uploader.options.mimetypes.join(', '),\n            );\n            fileInput.classList.add('ql-image');\n            fileInput.addEventListener('change', () => {\n              const range = this.quill.getSelection(true);\n              this.quill.uploader.upload(range, fileInput.files);\n              fileInput.value = '';\n            });\n            this.container.appendChild(fileInput);\n          }\n          fileInput.click();\n        },\n        video() {\n          this.quill.theme.tooltip.edit('video');\n        },\n      },\n    },\n  },\n});\n\nclass BaseTooltip extends Tooltip {\n  textbox: HTMLInputElement | null;\n  linkRange?: Range;\n\n  constructor(quill: Quill, boundsContainer?: HTMLElement) {\n    super(quill, boundsContainer);\n    this.textbox = this.root.querySelector('input[type=\"text\"]');\n    this.listen();\n  }\n\n  listen() {\n    // @ts-expect-error Fix me later\n    this.textbox.addEventListener('keydown', (event) => {\n      if (event.key === 'Enter') {\n        this.save();\n        event.preventDefault();\n      } else if (event.key === 'Escape') {\n        this.cancel();\n        event.preventDefault();\n      }\n    });\n  }\n\n  cancel() {\n    this.hide();\n    this.restoreFocus();\n  }\n\n  edit(mode = 'link', preview: string | null = null) {\n    this.root.classList.remove('ql-hidden');\n    this.root.classList.add('ql-editing');\n    if (this.textbox == null) return;\n\n    if (preview != null) {\n      this.textbox.value = preview;\n    } else if (mode !== this.root.getAttribute('data-mode')) {\n      this.textbox.value = '';\n    }\n    const bounds = this.quill.getBounds(this.quill.selection.savedRange);\n    if (bounds != null) {\n      this.position(bounds);\n    }\n    this.textbox.select();\n    this.textbox.setAttribute(\n      'placeholder',\n      this.textbox.getAttribute(`data-${mode}`) || '',\n    );\n    this.root.setAttribute('data-mode', mode);\n  }\n\n  restoreFocus() {\n    this.quill.focus({ preventScroll: true });\n  }\n\n  save() {\n    // @ts-expect-error Fix me later\n    let { value } = this.textbox;\n    switch (this.root.getAttribute('data-mode')) {\n      case 'link': {\n        const { scrollTop } = this.quill.root;\n        if (this.linkRange) {\n          this.quill.formatText(\n            this.linkRange,\n            'link',\n            value,\n            Emitter.sources.USER,\n          );\n          delete this.linkRange;\n        } else {\n          this.restoreFocus();\n          this.quill.format('link', value, Emitter.sources.USER);\n        }\n        this.quill.root.scrollTop = scrollTop;\n        break;\n      }\n      case 'video': {\n        value = extractVideoUrl(value);\n      } // eslint-disable-next-line no-fallthrough\n      case 'formula': {\n        if (!value) break;\n        const range = this.quill.getSelection(true);\n        if (range != null) {\n          const index = range.index + range.length;\n          this.quill.insertEmbed(\n            index,\n            // @ts-expect-error Fix me later\n            this.root.getAttribute('data-mode'),\n            value,\n            Emitter.sources.USER,\n          );\n          if (this.root.getAttribute('data-mode') === 'formula') {\n            this.quill.insertText(index + 1, ' ', Emitter.sources.USER);\n          }\n          this.quill.setSelection(index + 2, Emitter.sources.USER);\n        }\n        break;\n      }\n      default:\n    }\n    // @ts-expect-error Fix me later\n    this.textbox.value = '';\n    this.hide();\n  }\n}\n\nfunction extractVideoUrl(url: string) {\n  let match =\n    url.match(\n      /^(?:(https?):\\/\\/)?(?:(?:www|m)\\.)?youtube\\.com\\/watch.*v=([a-zA-Z0-9_-]+)/,\n    ) ||\n    url.match(/^(?:(https?):\\/\\/)?(?:(?:www|m)\\.)?youtu\\.be\\/([a-zA-Z0-9_-]+)/);\n  if (match) {\n    return `${match[1] || 'https'}://www.youtube.com/embed/${\n      match[2]\n    }?showinfo=0`;\n  }\n  // eslint-disable-next-line no-cond-assign\n  if ((match = url.match(/^(?:(https?):\\/\\/)?(?:www\\.)?vimeo\\.com\\/(\\d+)/))) {\n    return `${match[1] || 'https'}://player.vimeo.com/video/${match[2]}/`;\n  }\n  return url;\n}\n\nfunction fillSelect(\n  select: HTMLSelectElement,\n  values: Array<string | boolean>,\n  defaultValue: unknown = false,\n) {\n  values.forEach((value) => {\n    const option = document.createElement('option');\n    if (value === defaultValue) {\n      option.setAttribute('selected', 'selected');\n    } else {\n      option.setAttribute('value', String(value));\n    }\n    select.appendChild(option);\n  });\n}\n\nexport { BaseTooltip, BaseTheme as default };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,WAAW;AAEjC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,KAAK,MAAM,kBAAkB;AAEpC,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,UAAU,MAAM,sBAAsB;AAC7C,OAAOC,MAAM,MAAM,iBAAiB;AACpC,OAAOC,OAAO,MAAM,kBAAkB;AAQtC,MAAMC,MAAM,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;AAEpD,MAAMC,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;AAED,MAAMC,KAAK,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,WAAW,CAAC;AAE3C,MAAMC,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC;AAEtC,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC;AAE/C,MAAMC,SAAS,SAASV,KAAK,CAAC;EAI5BW,WAAWA,CAACC,KAAY,EAAEC,OAAqB,EAAE;IAC/C,KAAK,CAACD,KAAK,EAAEC,OAAO,CAAC;IACrB,MAAMC,QAAQ,GAAIC,CAAa,IAAK;MAClC,IAAI,CAACC,QAAQ,CAACC,IAAI,CAACC,QAAQ,CAACN,KAAK,CAACO,IAAI,CAAC,EAAE;QACvCH,QAAQ,CAACC,IAAI,CAACG,mBAAmB,CAAC,OAAO,EAAEN,QAAQ,CAAC;QACpD;MACF;MACA,IACE,IAAI,CAACO,OAAO,IAAI,IAAI;MACpB;MACA,CAAC,IAAI,CAACA,OAAO,CAACF,IAAI,CAACD,QAAQ,CAACH,CAAC,CAACO,MAAM,CAAC;MACrC;MACAN,QAAQ,CAACO,aAAa,KAAK,IAAI,CAACF,OAAO,CAACG,OAAO,IAC/C,CAAC,IAAI,CAACZ,KAAK,CAACa,QAAQ,CAAC,CAAC,EACtB;QACA,IAAI,CAACJ,OAAO,CAACK,IAAI,CAAC,CAAC;MACrB;MACA,IAAI,IAAI,CAACC,OAAO,IAAI,IAAI,EAAE;QACxB,IAAI,CAACA,OAAO,CAACC,OAAO,CAAEC,MAAM,IAAK;UAC/B;UACA,IAAI,CAACA,MAAM,CAACC,SAAS,CAACZ,QAAQ,CAACH,CAAC,CAACO,MAAM,CAAC,EAAE;YACxCO,MAAM,CAACE,KAAK,CAAC,CAAC;UAChB;QACF,CAAC,CAAC;MACJ;IACF,CAAC;IACDnB,KAAK,CAACoB,OAAO,CAACC,SAAS,CAAC,OAAO,EAAEjB,QAAQ,CAACC,IAAI,EAAEH,QAAQ,CAAC;EAC3D;EAQAoB,SAASA,CAACC,IAAY,EAAE;IACtB,MAAMC,MAAM,GAAG,KAAK,CAACF,SAAS,CAACC,IAAI,CAAC;IACpC,IAAIA,IAAI,KAAK,SAAS,EAAE;MACtB;MACA,IAAI,CAACE,aAAa,CAACD,MAAM,CAAC;IAC5B;IACA,OAAOA,MAAM;EACf;EAEAE,YAAYA,CACVC,OAAgC,EAChCC,KAAsD,EACtD;IACAC,KAAK,CAACC,IAAI,CAACH,OAAO,CAAC,CAACX,OAAO,CAAEe,MAAM,IAAK;MACtC,MAAMC,SAAS,GAAGD,MAAM,CAACE,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE;MACpDD,SAAS,CAACE,KAAK,CAAC,KAAK,CAAC,CAAClB,OAAO,CAAEO,IAAI,IAAK;QACvC,IAAI,CAACA,IAAI,CAACY,UAAU,CAAC,KAAK,CAAC,EAAE;QAC7BZ,IAAI,GAAGA,IAAI,CAACa,KAAK,CAAC,KAAK,CAACC,MAAM,CAAC;QAC/B,IAAIT,KAAK,CAACL,IAAI,CAAC,IAAI,IAAI,EAAE;QACzB,IAAIA,IAAI,KAAK,WAAW,EAAE;UACxB;UACAQ,MAAM,CAACO,SAAS,GAAGV,KAAK,CAACL,IAAI,CAAC,CAAC,EAAE,CAAC,GAAGK,KAAK,CAACL,IAAI,CAAC,CAACgB,GAAG;QACtD,CAAC,MAAM,IAAI,OAAOX,KAAK,CAACL,IAAI,CAAC,KAAK,QAAQ,EAAE;UAC1C;UACAQ,MAAM,CAACO,SAAS,GAAGV,KAAK,CAACL,IAAI,CAAC;QAChC,CAAC,MAAM;UACL;UACA,MAAMiB,KAAK,GAAGT,MAAM,CAACS,KAAK,IAAI,EAAE;UAChC;UACA,IAAIA,KAAK,IAAI,IAAI,IAAIZ,KAAK,CAACL,IAAI,CAAC,CAACiB,KAAK,CAAC,EAAE;YACvC;YACAT,MAAM,CAACO,SAAS,GAAGV,KAAK,CAACL,IAAI,CAAC,CAACiB,KAAK,CAAC;UACvC;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAC,YAAYA,CACVC,OAAsC,EACtCd,KAAsD,EACtD;IACA,IAAI,CAACb,OAAO,GAAGc,KAAK,CAACC,IAAI,CAACY,OAAO,CAAC,CAACC,GAAG,CAAEC,MAAM,IAAK;MACjD,IAAIA,MAAM,CAACC,SAAS,CAACvC,QAAQ,CAAC,UAAU,CAAC,EAAE;QACzC,IAAIsC,MAAM,CAACE,aAAa,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE;UAC1CC,UAAU,CAACH,MAAM,EAAEnD,MAAM,CAAC;QAC5B;QACA,IAAI,OAAOmC,KAAK,CAACoB,KAAK,KAAK,QAAQ,EAAE;UACnC,OAAO,IAAI1D,UAAU,CAACsD,MAAM,EAAEhB,KAAK,CAACoB,KAAK,CAAC;QAC5C;MACF;MACA,IACEJ,MAAM,CAACC,SAAS,CAACvC,QAAQ,CAAC,eAAe,CAAC,IAC1CsC,MAAM,CAACC,SAAS,CAACvC,QAAQ,CAAC,UAAU,CAAC,EACrC;QACA,MAAM2C,MAAM,GAAGL,MAAM,CAACC,SAAS,CAACvC,QAAQ,CAAC,eAAe,CAAC,GACrD,YAAY,GACZ,OAAO;QACX,IAAIsC,MAAM,CAACE,aAAa,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE;UAC1CC,UAAU,CACRH,MAAM,EACNlD,MAAM,EACNuD,MAAM,KAAK,YAAY,GAAG,SAAS,GAAG,SACxC,CAAC;QACH;QACA,OAAO,IAAI5D,WAAW,CAACuD,MAAM,EAAEhB,KAAK,CAACqB,MAAM,CAAW,CAAC;MACzD;MACA,IAAIL,MAAM,CAACE,aAAa,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE;QAC1C,IAAIF,MAAM,CAACC,SAAS,CAACvC,QAAQ,CAAC,SAAS,CAAC,EAAE;UACxCyC,UAAU,CAACH,MAAM,EAAEjD,KAAK,CAAC;QAC3B,CAAC,MAAM,IAAIiD,MAAM,CAACC,SAAS,CAACvC,QAAQ,CAAC,WAAW,CAAC,EAAE;UACjDyC,UAAU,CAACH,MAAM,EAAEhD,OAAO,CAAC;QAC7B,CAAC,MAAM,IAAIgD,MAAM,CAACC,SAAS,CAACvC,QAAQ,CAAC,SAAS,CAAC,EAAE;UAC/CyC,UAAU,CAACH,MAAM,EAAE/C,KAAK,CAAC;QAC3B;MACF;MACA,OAAO,IAAIN,MAAM,CAACqD,MAAM,CAAC;IAC3B,CAAC,CAAC;IACF,MAAMM,MAAM,GAAGA,CAAA,KAAM;MACnB,IAAI,CAACnC,OAAO,CAACC,OAAO,CAAEC,MAAM,IAAK;QAC/BA,MAAM,CAACiC,MAAM,CAAC,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC;IACD,IAAI,CAAClD,KAAK,CAACmD,EAAE,CAAChE,OAAO,CAACiE,MAAM,CAACC,aAAa,EAAEH,MAAM,CAAC;EACrD;AACF;AACApD,SAAS,CAACwD,QAAQ,GAAGpE,KAAK,CAAC,CAAC,CAAC,EAAEE,KAAK,CAACkE,QAAQ,EAAE;EAC7CC,OAAO,EAAE;IACPC,OAAO,EAAE;MACPC,QAAQ,EAAE;QACRC,OAAOA,CAAA,EAAG;UACR,IAAI,CAAC1D,KAAK,CAAC2D,KAAK,CAAClD,OAAO,CAACmD,IAAI,CAAC,SAAS,CAAC;QAC1C,CAAC;QACDC,KAAKA,CAAA,EAAG;UACN,IAAIC,SAAS,GAAG,IAAI,CAAC5C,SAAS,CAAC4B,aAAa,CAC1C,2BACF,CAAC;UACD,IAAIgB,SAAS,IAAI,IAAI,EAAE;YACrBA,SAAS,GAAG1D,QAAQ,CAAC2D,aAAa,CAAC,OAAO,CAAC;YAC3CD,SAAS,CAACE,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC;YACtCF,SAAS,CAACE,YAAY,CACpB,QAAQ,EACR,IAAI,CAAChE,KAAK,CAACiE,QAAQ,CAAChE,OAAO,CAACiE,SAAS,CAACC,IAAI,CAAC,IAAI,CACjD,CAAC;YACDL,SAAS,CAACjB,SAAS,CAACuB,GAAG,CAAC,UAAU,CAAC;YACnCN,SAAS,CAACO,gBAAgB,CAAC,QAAQ,EAAE,MAAM;cACzC,MAAMC,KAAK,GAAG,IAAI,CAACtE,KAAK,CAACuE,YAAY,CAAC,IAAI,CAAC;cAC3C,IAAI,CAACvE,KAAK,CAACiE,QAAQ,CAACO,MAAM,CAACF,KAAK,EAAER,SAAS,CAACW,KAAK,CAAC;cAClDX,SAAS,CAACtB,KAAK,GAAG,EAAE;YACtB,CAAC,CAAC;YACF,IAAI,CAACtB,SAAS,CAACwD,WAAW,CAACZ,SAAS,CAAC;UACvC;UACAA,SAAS,CAACa,KAAK,CAAC,CAAC;QACnB,CAAC;QACDC,KAAKA,CAAA,EAAG;UACN,IAAI,CAAC5E,KAAK,CAAC2D,KAAK,CAAClD,OAAO,CAACmD,IAAI,CAAC,OAAO,CAAC;QACxC;MACF;IACF;EACF;AACF,CAAC,CAAC;AAEF,MAAMiB,WAAW,SAASrF,OAAO,CAAC;EAIhCO,WAAWA,CAACC,KAAY,EAAE8E,eAA6B,EAAE;IACvD,KAAK,CAAC9E,KAAK,EAAE8E,eAAe,CAAC;IAC7B,IAAI,CAAClE,OAAO,GAAG,IAAI,CAACL,IAAI,CAACuC,aAAa,CAAC,oBAAoB,CAAC;IAC5D,IAAI,CAACiC,MAAM,CAAC,CAAC;EACf;EAEAA,MAAMA,CAAA,EAAG;IACP;IACA,IAAI,CAACnE,OAAO,CAACyD,gBAAgB,CAAC,SAAS,EAAGW,KAAK,IAAK;MAClD,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,EAAE;QACzB,IAAI,CAACC,IAAI,CAAC,CAAC;QACXF,KAAK,CAACG,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM,IAAIH,KAAK,CAACC,GAAG,KAAK,QAAQ,EAAE;QACjC,IAAI,CAACG,MAAM,CAAC,CAAC;QACbJ,KAAK,CAACG,cAAc,CAAC,CAAC;MACxB;IACF,CAAC,CAAC;EACJ;EAEAC,MAAMA,CAAA,EAAG;IACP,IAAI,CAACtE,IAAI,CAAC,CAAC;IACX,IAAI,CAACuE,YAAY,CAAC,CAAC;EACrB;EAEAzB,IAAIA,CAAA,EAA+C;IAAA,IAA9C0B,IAAI,GAAAC,SAAA,CAAAlD,MAAA,QAAAkD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,MAAM;IAAA,IAAEE,OAAsB,GAAAF,SAAA,CAAAlD,MAAA,QAAAkD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI;IAC/C,IAAI,CAAChF,IAAI,CAACsC,SAAS,CAAC6C,MAAM,CAAC,WAAW,CAAC;IACvC,IAAI,CAACnF,IAAI,CAACsC,SAAS,CAACuB,GAAG,CAAC,YAAY,CAAC;IACrC,IAAI,IAAI,CAACxD,OAAO,IAAI,IAAI,EAAE;IAE1B,IAAI6E,OAAO,IAAI,IAAI,EAAE;MACnB,IAAI,CAAC7E,OAAO,CAAC4B,KAAK,GAAGiD,OAAO;IAC9B,CAAC,MAAM,IAAIH,IAAI,KAAK,IAAI,CAAC/E,IAAI,CAAC0B,YAAY,CAAC,WAAW,CAAC,EAAE;MACvD,IAAI,CAACrB,OAAO,CAAC4B,KAAK,GAAG,EAAE;IACzB;IACA,MAAMmD,MAAM,GAAG,IAAI,CAAC3F,KAAK,CAAC4F,SAAS,CAAC,IAAI,CAAC5F,KAAK,CAAC6F,SAAS,CAACC,UAAU,CAAC;IACpE,IAAIH,MAAM,IAAI,IAAI,EAAE;MAClB,IAAI,CAACI,QAAQ,CAACJ,MAAM,CAAC;IACvB;IACA,IAAI,CAAC/E,OAAO,CAACgC,MAAM,CAAC,CAAC;IACrB,IAAI,CAAChC,OAAO,CAACoD,YAAY,CACvB,aAAa,EACb,IAAI,CAACpD,OAAO,CAACqB,YAAY,CAAE,QAAOqD,IAAK,EAAC,CAAC,IAAI,EAC/C,CAAC;IACD,IAAI,CAAC/E,IAAI,CAACyD,YAAY,CAAC,WAAW,EAAEsB,IAAI,CAAC;EAC3C;EAEAD,YAAYA,CAAA,EAAG;IACb,IAAI,CAACrF,KAAK,CAACgG,KAAK,CAAC;MAAEC,aAAa,EAAE;IAAK,CAAC,CAAC;EAC3C;EAEAf,IAAIA,CAAA,EAAG;IACL;IACA,IAAI;MAAE1C;IAAM,CAAC,GAAG,IAAI,CAAC5B,OAAO;IAC5B,QAAQ,IAAI,CAACL,IAAI,CAAC0B,YAAY,CAAC,WAAW,CAAC;MACzC,KAAK,MAAM;QAAE;UACX,MAAM;YAAEiE;UAAU,CAAC,GAAG,IAAI,CAAClG,KAAK,CAACO,IAAI;UACrC,IAAI,IAAI,CAAC4F,SAAS,EAAE;YAClB,IAAI,CAACnG,KAAK,CAACoG,UAAU,CACnB,IAAI,CAACD,SAAS,EACd,MAAM,EACN3D,KAAK,EACLrD,OAAO,CAACkH,OAAO,CAACC,IAClB,CAAC;YACD,OAAO,IAAI,CAACH,SAAS;UACvB,CAAC,MAAM;YACL,IAAI,CAACd,YAAY,CAAC,CAAC;YACnB,IAAI,CAACrF,KAAK,CAACiD,MAAM,CAAC,MAAM,EAAET,KAAK,EAAErD,OAAO,CAACkH,OAAO,CAACC,IAAI,CAAC;UACxD;UACA,IAAI,CAACtG,KAAK,CAACO,IAAI,CAAC2F,SAAS,GAAGA,SAAS;UACrC;QACF;MACA,KAAK,OAAO;QAAE;UACZ1D,KAAK,GAAG+D,eAAe,CAAC/D,KAAK,CAAC;QAChC;MAAE;MACF,KAAK,SAAS;QAAE;UACd,IAAI,CAACA,KAAK,EAAE;UACZ,MAAM8B,KAAK,GAAG,IAAI,CAACtE,KAAK,CAACuE,YAAY,CAAC,IAAI,CAAC;UAC3C,IAAID,KAAK,IAAI,IAAI,EAAE;YACjB,MAAMkC,KAAK,GAAGlC,KAAK,CAACkC,KAAK,GAAGlC,KAAK,CAACjC,MAAM;YACxC,IAAI,CAACrC,KAAK,CAACyG,WAAW,CACpBD,KAAK;YACL;YACA,IAAI,CAACjG,IAAI,CAAC0B,YAAY,CAAC,WAAW,CAAC,EACnCO,KAAK,EACLrD,OAAO,CAACkH,OAAO,CAACC,IAClB,CAAC;YACD,IAAI,IAAI,CAAC/F,IAAI,CAAC0B,YAAY,CAAC,WAAW,CAAC,KAAK,SAAS,EAAE;cACrD,IAAI,CAACjC,KAAK,CAAC0G,UAAU,CAACF,KAAK,GAAG,CAAC,EAAE,GAAG,EAAErH,OAAO,CAACkH,OAAO,CAACC,IAAI,CAAC;YAC7D;YACA,IAAI,CAACtG,KAAK,CAAC2G,YAAY,CAACH,KAAK,GAAG,CAAC,EAAErH,OAAO,CAACkH,OAAO,CAACC,IAAI,CAAC;UAC1D;UACA;QACF;MACA;IACF;IACA;IACA,IAAI,CAAC1F,OAAO,CAAC4B,KAAK,GAAG,EAAE;IACvB,IAAI,CAAC1B,IAAI,CAAC,CAAC;EACb;AACF;AAEA,SAASyF,eAAeA,CAACK,GAAW,EAAE;EACpC,IAAIC,KAAK,GACPD,GAAG,CAACC,KAAK,CACP,4EACF,CAAC,IACDD,GAAG,CAACC,KAAK,CAAC,gEAAgE,CAAC;EAC7E,IAAIA,KAAK,EAAE;IACT,OAAQ,GAAEA,KAAK,CAAC,CAAC,CAAC,IAAI,OAAQ,4BAC5BA,KAAK,CAAC,CAAC,CACR,aAAY;EACf;EACA;EACA,IAAKA,KAAK,GAAGD,GAAG,CAACC,KAAK,CAAC,gDAAgD,CAAC,EAAG;IACzE,OAAQ,GAAEA,KAAK,CAAC,CAAC,CAAC,IAAI,OAAQ,6BAA4BA,KAAK,CAAC,CAAC,CAAE,GAAE;EACvE;EACA,OAAOD,GAAG;AACZ;AAEA,SAAS7D,UAAUA,CACjBH,MAAyB,EACzBkE,MAA+B,EAE/B;EAAA,IADAC,YAAqB,GAAAxB,SAAA,CAAAlD,MAAA,QAAAkD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;EAE7BuB,MAAM,CAAC9F,OAAO,CAAEwB,KAAK,IAAK;IACxB,MAAMwE,MAAM,GAAG5G,QAAQ,CAAC2D,aAAa,CAAC,QAAQ,CAAC;IAC/C,IAAIvB,KAAK,KAAKuE,YAAY,EAAE;MAC1BC,MAAM,CAAChD,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC;IAC7C,CAAC,MAAM;MACLgD,MAAM,CAAChD,YAAY,CAAC,OAAO,EAAEiD,MAAM,CAACzE,KAAK,CAAC,CAAC;IAC7C;IACAI,MAAM,CAAC8B,WAAW,CAACsC,MAAM,CAAC;EAC5B,CAAC,CAAC;AACJ;AAEA,SAASnC,WAAW,EAAE/E,SAAS,IAAIoH,OAAO", "ignoreList": []}