{"version": 3, "file": "script.js", "names": ["Inline", "<PERSON><PERSON><PERSON>", "blotName", "tagName", "create", "value", "document", "createElement", "formats", "domNode", "undefined"], "sources": ["../../src/formats/script.ts"], "sourcesContent": ["import Inline from '../blots/inline.js';\n\nclass Script extends Inline {\n  static blotName = 'script';\n  static tagName = ['SUB', 'SUP'];\n\n  static create(value: 'super' | 'sub' | (string & {})) {\n    if (value === 'super') {\n      return document.createElement('sup');\n    }\n    if (value === 'sub') {\n      return document.createElement('sub');\n    }\n    return super.create(value);\n  }\n\n  static formats(domNode: HTMLElement) {\n    if (domNode.tagName === 'SUB') return 'sub';\n    if (domNode.tagName === 'SUP') return 'super';\n    return undefined;\n  }\n}\n\nexport default Script;\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AAEvC,MAAMC,MAAM,SAASD,MAAM,CAAC;EAC1B,OAAOE,QAAQ,GAAG,QAAQ;EAC1B,OAAOC,OAAO,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC;EAE/B,OAAOC,MAAMA,CAACC,KAAsC,EAAE;IACpD,IAAIA,KAAK,KAAK,OAAO,EAAE;MACrB,OAAOC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACtC;IACA,IAAIF,KAAK,KAAK,KAAK,EAAE;MACnB,OAAOC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACtC;IACA,OAAO,KAAK,CAACH,MAAM,CAACC,KAAK,CAAC;EAC5B;EAEA,OAAOG,OAAOA,CAACC,OAAoB,EAAE;IACnC,IAAIA,OAAO,CAACN,OAAO,KAAK,KAAK,EAAE,OAAO,KAAK;IAC3C,IAAIM,OAAO,CAACN,OAAO,KAAK,KAAK,EAAE,OAAO,OAAO;IAC7C,OAAOO,SAAS;EAClB;AACF;AAEA,eAAeT,MAAM", "ignoreList": []}