import { useState, useEffect, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import axios from 'axios';

const AdminDashboard = () => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const { user, logout } = useContext(AuthContext);
  const navigate = useNavigate();
  
  useEffect(() => {
    fetchStats();
  }, []);
  
  const fetchStats = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${import.meta.env.VITE_API_URL}/api/admin/dashboard-stats`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      setStats(response.data.stats);
      setLoading(false);
    } catch (error) {
      setError('Failed to load dashboard stats');
      setLoading(false);
    }
  };
  
  const handleLogout = () => {
    logout();
    navigate('/admin-login');
  };
  
  const StatCard = ({ title, value, color, icon }) => (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="p-5">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className={`w-8 h-8 ${color} rounded-full flex items-center justify-center`}>
              <span className="text-white font-bold">{icon}</span>
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
              <dd className="text-lg font-medium text-gray-900">{value}</dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  );
  
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <p className="text-red-600">{error}</p>
          <button
            onClick={fetchStats}
            className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="text-gray-600">Welcome back, {user?.name}!</p>
            </div>
            <div className="flex space-x-4">
              <button
                onClick={() => navigate('/admin/users')}
                className="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
              >
                Manage Users
              </button>
              <button
                onClick={() => navigate('/admin/agreements')}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                Manage Agreements
              </button>
              <button
                onClick={handleLogout}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>
      
      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          
          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <StatCard
              title="Total Users"
              value={stats?.totalUsers || 0}
              color="bg-blue-500"
              icon="👥"
            />
            <StatCard
              title="Verified Users"
              value={stats?.verifiedUsers || 0}
              color="bg-green-500"
              icon="✓"
            />
            <StatCard
              title="Signed Agreements"
              value={stats?.signedUsers || 0}
              color="bg-purple-500"
              icon="📝"
            />
            <StatCard
              title="Working Users"
              value={stats?.workingUsers || 0}
              color="bg-yellow-500"
              icon="⚡"
            />
            <StatCard
              title="Submitted Work"
              value={stats?.submittedUsers || 0}
              color="bg-indigo-500"
              icon="📤"
            />
            <StatCard
              title="Penalized Users"
              value={stats?.penalizedUsers || 0}
              color="bg-red-500"
              icon="⚠"
            />
          </div>
          
          {/* Quick Actions */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-6">
                Quick Actions
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <button
                  onClick={() => navigate('/admin/users')}
                  className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-center"
                >
                  <div className="text-2xl mb-2">👥</div>
                  <div className="font-medium">Manage Users</div>
                  <div className="text-sm text-gray-500">Create, edit, delete users</div>
                </button>
                
                <button
                  onClick={() => navigate('/admin/agreements')}
                  className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-center"
                >
                  <div className="text-2xl mb-2">📝</div>
                  <div className="font-medium">Agreements</div>
                  <div className="text-sm text-gray-500">Manage agreement content</div>
                </button>
                
                <button
                  onClick={() => navigate('/admin/users')}
                  className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-center"
                >
                  <div className="text-2xl mb-2">📧</div>
                  <div className="font-medium">Send OTP</div>
                  <div className="text-sm text-gray-500">Send OTP to users</div>
                </button>
                
                <button
                  onClick={() => navigate('/admin/users')}
                  className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-center"
                >
                  <div className="text-2xl mb-2">🔗</div>
                  <div className="font-medium">Agreement Links</div>
                  <div className="text-sm text-gray-500">Send agreement links</div>
                </button>
              </div>
            </div>
          </div>
          
          {/* Recent Activity */}
          <div className="mt-8 bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-6">
                System Overview
              </h3>
              
              <div className="space-y-4">
                <div className="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                  <div>
                    <div className="font-medium">User Registration Rate</div>
                    <div className="text-sm text-gray-500">
                      {stats?.verifiedUsers || 0} out of {stats?.totalUsers || 0} users verified
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-green-600">
                      {stats?.totalUsers > 0 ? Math.round((stats.verifiedUsers / stats.totalUsers) * 100) : 0}%
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                  <div>
                    <div className="font-medium">Agreement Completion Rate</div>
                    <div className="text-sm text-gray-500">
                      {stats?.signedUsers || 0} out of {stats?.verifiedUsers || 0} verified users signed
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-blue-600">
                      {stats?.verifiedUsers > 0 ? Math.round((stats.signedUsers / stats.verifiedUsers) * 100) : 0}%
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                  <div>
                    <div className="font-medium">Work Completion Rate</div>
                    <div className="text-sm text-gray-500">
                      {stats?.submittedUsers || 0} out of {stats?.signedUsers || 0} signed users submitted
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-purple-600">
                      {stats?.signedUsers > 0 ? Math.round((stats.submittedUsers / stats.signedUsers) * 100) : 0}%
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default AdminDashboard;
