{"version": 3, "file": "scrollRectIntoView.js", "names": ["getParentElement", "element", "parentElement", "getRootNode", "host", "getElementRect", "rect", "getBoundingClientRect", "scaleX", "Math", "abs", "width", "offsetWidth", "scaleY", "height", "offsetHeight", "top", "right", "left", "clientWidth", "bottom", "clientHeight", "paddingValueToInt", "value", "number", "parseInt", "Number", "isNaN", "getScrollDistance", "targetStart", "targetEnd", "scrollStart", "scrollEnd", "scrollPaddingStart", "scrollPaddingEnd", "scrollRectIntoView", "root", "targetRect", "document", "ownerDocument", "current", "isDocumentBody", "body", "bounding", "window", "visualViewport", "documentElement", "style", "getComputedStyle", "scrollDistanceX", "scrollPaddingLeft", "scrollPaddingRight", "scrollDistanceY", "scrollPaddingTop", "scrollPaddingBottom", "defaultView", "scrollBy", "scrollLeft", "scrollTop", "scrolledLeft", "scrolledTop", "position"], "sources": ["../../../src/core/utils/scrollRectIntoView.ts"], "sourcesContent": ["export type Rect = {\n  top: number;\n  right: number;\n  bottom: number;\n  left: number;\n};\n\nconst getParentElement = (element: Node): Element | null =>\n  element.parentElement || (element.getRootNode() as ShadowRoot).host || null;\n\nconst getElementRect = (element: Element): Rect => {\n  const rect = element.getBoundingClientRect();\n  const scaleX =\n    ('offsetWidth' in element &&\n      Math.abs(rect.width) / (element as HTMLElement).offsetWidth) ||\n    1;\n  const scaleY =\n    ('offsetHeight' in element &&\n      Math.abs(rect.height) / (element as HTMLElement).offsetHeight) ||\n    1;\n  return {\n    top: rect.top,\n    right: rect.left + element.clientWidth * scaleX,\n    bottom: rect.top + element.clientHeight * scaleY,\n    left: rect.left,\n  };\n};\n\nconst paddingValueToInt = (value: string) => {\n  const number = parseInt(value, 10);\n  return Number.isNaN(number) ? 0 : number;\n};\n\n// Follow the steps described in https://www.w3.org/TR/cssom-view-1/#element-scrolling-members,\n// assuming that the scroll option is set to 'nearest'.\nconst getScrollDistance = (\n  targetStart: number,\n  targetEnd: number,\n  scrollStart: number,\n  scrollEnd: number,\n  scrollPaddingStart: number,\n  scrollPaddingEnd: number,\n) => {\n  if (targetStart < scrollStart && targetEnd > scrollEnd) {\n    return 0;\n  }\n\n  if (targetStart < scrollStart) {\n    return -(scrollStart - targetStart + scrollPaddingStart);\n  }\n\n  if (targetEnd > scrollEnd) {\n    return targetEnd - targetStart > scrollEnd - scrollStart\n      ? targetStart + scrollPaddingStart - scrollStart\n      : targetEnd - scrollEnd + scrollPaddingEnd;\n  }\n  return 0;\n};\n\nconst scrollRectIntoView = (root: HTMLElement, targetRect: Rect) => {\n  const document = root.ownerDocument;\n\n  let rect = targetRect;\n\n  let current: Element | null = root;\n  while (current) {\n    const isDocumentBody: boolean = current === document.body;\n    const bounding = isDocumentBody\n      ? {\n          top: 0,\n          right:\n            window.visualViewport?.width ??\n            document.documentElement.clientWidth,\n          bottom:\n            window.visualViewport?.height ??\n            document.documentElement.clientHeight,\n          left: 0,\n        }\n      : getElementRect(current);\n\n    const style = getComputedStyle(current);\n    const scrollDistanceX = getScrollDistance(\n      rect.left,\n      rect.right,\n      bounding.left,\n      bounding.right,\n      paddingValueToInt(style.scrollPaddingLeft),\n      paddingValueToInt(style.scrollPaddingRight),\n    );\n    const scrollDistanceY = getScrollDistance(\n      rect.top,\n      rect.bottom,\n      bounding.top,\n      bounding.bottom,\n      paddingValueToInt(style.scrollPaddingTop),\n      paddingValueToInt(style.scrollPaddingBottom),\n    );\n    if (scrollDistanceX || scrollDistanceY) {\n      if (isDocumentBody) {\n        document.defaultView?.scrollBy(scrollDistanceX, scrollDistanceY);\n      } else {\n        const { scrollLeft, scrollTop } = current;\n        if (scrollDistanceY) {\n          current.scrollTop += scrollDistanceY;\n        }\n        if (scrollDistanceX) {\n          current.scrollLeft += scrollDistanceX;\n        }\n        const scrolledLeft = current.scrollLeft - scrollLeft;\n        const scrolledTop = current.scrollTop - scrollTop;\n        rect = {\n          left: rect.left - scrolledLeft,\n          top: rect.top - scrolledTop,\n          right: rect.right - scrolledLeft,\n          bottom: rect.bottom - scrolledTop,\n        };\n      }\n    }\n\n    current =\n      isDocumentBody || style.position === 'fixed'\n        ? null\n        : getParentElement(current);\n  }\n};\n\nexport default scrollRectIntoView;\n"], "mappings": "AAOA,MAAMA,gBAAgB,GAAIC,OAAa,IACrCA,OAAO,CAACC,aAAa,IAAKD,OAAO,CAACE,WAAW,CAAC,CAAC,CAAgBC,IAAI,IAAI,IAAI;AAE7E,MAAMC,cAAc,GAAIJ,OAAgB,IAAW;EACjD,MAAMK,IAAI,GAAGL,OAAO,CAACM,qBAAqB,CAAC,CAAC;EAC5C,MAAMC,MAAM,GACT,aAAa,IAAIP,OAAO,IACvBQ,IAAI,CAACC,GAAG,CAACJ,IAAI,CAACK,KAAK,CAAC,GAAIV,OAAO,CAAiBW,WAAW,IAC7D,CAAC;EACH,MAAMC,MAAM,GACT,cAAc,IAAIZ,OAAO,IACxBQ,IAAI,CAACC,GAAG,CAACJ,IAAI,CAACQ,MAAM,CAAC,GAAIb,OAAO,CAAiBc,YAAY,IAC/D,CAAC;EACH,OAAO;IACLC,GAAG,EAAEV,IAAI,CAACU,GAAG;IACbC,KAAK,EAAEX,IAAI,CAACY,IAAI,GAAGjB,OAAO,CAACkB,WAAW,GAAGX,MAAM;IAC/CY,MAAM,EAAEd,IAAI,CAACU,GAAG,GAAGf,OAAO,CAACoB,YAAY,GAAGR,MAAM;IAChDK,IAAI,EAAEZ,IAAI,CAACY;EACb,CAAC;AACH,CAAC;AAED,MAAMI,iBAAiB,GAAIC,KAAa,IAAK;EAC3C,MAAMC,MAAM,GAAGC,QAAQ,CAACF,KAAK,EAAE,EAAE,CAAC;EAClC,OAAOG,MAAM,CAACC,KAAK,CAACH,MAAM,CAAC,GAAG,CAAC,GAAGA,MAAM;AAC1C,CAAC;;AAED;AACA;AACA,MAAMI,iBAAiB,GAAGA,CACxBC,WAAmB,EACnBC,SAAiB,EACjBC,WAAmB,EACnBC,SAAiB,EACjBC,kBAA0B,EAC1BC,gBAAwB,KACrB;EACH,IAAIL,WAAW,GAAGE,WAAW,IAAID,SAAS,GAAGE,SAAS,EAAE;IACtD,OAAO,CAAC;EACV;EAEA,IAAIH,WAAW,GAAGE,WAAW,EAAE;IAC7B,OAAO,EAAEA,WAAW,GAAGF,WAAW,GAAGI,kBAAkB,CAAC;EAC1D;EAEA,IAAIH,SAAS,GAAGE,SAAS,EAAE;IACzB,OAAOF,SAAS,GAAGD,WAAW,GAAGG,SAAS,GAAGD,WAAW,GACpDF,WAAW,GAAGI,kBAAkB,GAAGF,WAAW,GAC9CD,SAAS,GAAGE,SAAS,GAAGE,gBAAgB;EAC9C;EACA,OAAO,CAAC;AACV,CAAC;AAED,MAAMC,kBAAkB,GAAGA,CAACC,IAAiB,EAAEC,UAAgB,KAAK;EAClE,MAAMC,QAAQ,GAAGF,IAAI,CAACG,aAAa;EAEnC,IAAIjC,IAAI,GAAG+B,UAAU;EAErB,IAAIG,OAAuB,GAAGJ,IAAI;EAClC,OAAOI,OAAO,EAAE;IACd,MAAMC,cAAuB,GAAGD,OAAO,KAAKF,QAAQ,CAACI,IAAI;IACzD,MAAMC,QAAQ,GAAGF,cAAc,GAC3B;MACEzB,GAAG,EAAE,CAAC;MACNC,KAAK,EACH2B,MAAM,CAACC,cAAc,EAAElC,KAAK,IAC5B2B,QAAQ,CAACQ,eAAe,CAAC3B,WAAW;MACtCC,MAAM,EACJwB,MAAM,CAACC,cAAc,EAAE/B,MAAM,IAC7BwB,QAAQ,CAACQ,eAAe,CAACzB,YAAY;MACvCH,IAAI,EAAE;IACR,CAAC,GACDb,cAAc,CAACmC,OAAO,CAAC;IAE3B,MAAMO,KAAK,GAAGC,gBAAgB,CAACR,OAAO,CAAC;IACvC,MAAMS,eAAe,GAAGrB,iBAAiB,CACvCtB,IAAI,CAACY,IAAI,EACTZ,IAAI,CAACW,KAAK,EACV0B,QAAQ,CAACzB,IAAI,EACbyB,QAAQ,CAAC1B,KAAK,EACdK,iBAAiB,CAACyB,KAAK,CAACG,iBAAiB,CAAC,EAC1C5B,iBAAiB,CAACyB,KAAK,CAACI,kBAAkB,CAC5C,CAAC;IACD,MAAMC,eAAe,GAAGxB,iBAAiB,CACvCtB,IAAI,CAACU,GAAG,EACRV,IAAI,CAACc,MAAM,EACXuB,QAAQ,CAAC3B,GAAG,EACZ2B,QAAQ,CAACvB,MAAM,EACfE,iBAAiB,CAACyB,KAAK,CAACM,gBAAgB,CAAC,EACzC/B,iBAAiB,CAACyB,KAAK,CAACO,mBAAmB,CAC7C,CAAC;IACD,IAAIL,eAAe,IAAIG,eAAe,EAAE;MACtC,IAAIX,cAAc,EAAE;QAClBH,QAAQ,CAACiB,WAAW,EAAEC,QAAQ,CAACP,eAAe,EAAEG,eAAe,CAAC;MAClE,CAAC,MAAM;QACL,MAAM;UAAEK,UAAU;UAAEC;QAAU,CAAC,GAAGlB,OAAO;QACzC,IAAIY,eAAe,EAAE;UACnBZ,OAAO,CAACkB,SAAS,IAAIN,eAAe;QACtC;QACA,IAAIH,eAAe,EAAE;UACnBT,OAAO,CAACiB,UAAU,IAAIR,eAAe;QACvC;QACA,MAAMU,YAAY,GAAGnB,OAAO,CAACiB,UAAU,GAAGA,UAAU;QACpD,MAAMG,WAAW,GAAGpB,OAAO,CAACkB,SAAS,GAAGA,SAAS;QACjDpD,IAAI,GAAG;UACLY,IAAI,EAAEZ,IAAI,CAACY,IAAI,GAAGyC,YAAY;UAC9B3C,GAAG,EAAEV,IAAI,CAACU,GAAG,GAAG4C,WAAW;UAC3B3C,KAAK,EAAEX,IAAI,CAACW,KAAK,GAAG0C,YAAY;UAChCvC,MAAM,EAAEd,IAAI,CAACc,MAAM,GAAGwC;QACxB,CAAC;MACH;IACF;IAEApB,OAAO,GACLC,cAAc,IAAIM,KAAK,CAACc,QAAQ,KAAK,OAAO,GACxC,IAAI,GACJ7D,gBAAgB,CAACwC,OAAO,CAAC;EACjC;AACF,CAAC;AAED,eAAeL,kBAAkB", "ignoreList": []}